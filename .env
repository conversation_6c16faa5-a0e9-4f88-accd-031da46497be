# ChartGenius v2.0 Environment Variables

# JWT Configuration
JWT_SECRET_KEY=34sSDF542rf65EJ1kj

# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************

# CryptoCompare Configuration
CRYPTOCOMPARE_API_KEY=****************************************************************

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
ADMIN_TELEGRAM_ID=299820674
WEBAPP_URL=https://your-webapp-url.com

# Oracle Autonomous JSON Database Configuration
ORACLE_USERNAME=ADMIN
ORACLE_PASSWORD=-QDC2xg!Ecj2s@h1
ORACLE_DSN=(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.eu-frankfurt-1.oraclecloud.com))(connect_data=(service_name=g2fbf778b2604d0_chartgenius2_medium.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))
# ORACLE_WALLET_LOCATION=/path/to/wallet (not needed for Always Free AJD)
# ORACLE_WALLET_PASSWORD=your_wallet_password

# Oracle Connection Pool Settings
ORACLE_POOL_MIN=2
ORACLE_POOL_MAX=10
ORACLE_POOL_INCREMENT=1

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_TTL_OHLCV=900  # 15 minutes in seconds

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Application Configuration
DEFAULT_SYMBOL=BTCUSDT
DEFAULT_INTERVAL=4h
DEFAULT_DAYS=15

# LLM Configuration
DEFAULT_LLM_PROVIDER=openai  # openai or gemini

# Development/Production Mode
ENVIRONMENT=development  # development or production

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/chartgenius.log

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,https://t.me

# Cache Configuration
ENABLE_CACHE=false
CACHE_TTL_SECONDS=900

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=8001
