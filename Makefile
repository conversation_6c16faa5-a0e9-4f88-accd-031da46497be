# ChartGenius v2.0 Makefile

.PHONY: help dev prod install clean test lint migrate deploy

# Default target
help:
	@echo "ChartGenius v2.0 - Available commands:"
	@echo ""
	@echo "Development:"
	@echo "  make dev          - Start development environment"
	@echo "  make install      - Install all dependencies"
	@echo "  make clean        - Clean temporary files"
	@echo ""
	@echo "Database:"
	@echo "  make migrate      - Migrate data from Firestore to Oracle AJD"
	@echo "  make db-setup     - Setup Oracle AJD tables"
	@echo ""
	@echo "Testing:"
	@echo "  make test         - Run all tests"
	@echo "  make lint         - Run linting"
	@echo ""
	@echo "Production:"
	@echo "  make prod         - Start production environment"
	@echo "  make deploy       - Deploy to OCI"
	@echo ""
	@echo "Utilities:"
	@echo "  make logs         - Show application logs"
	@echo "  make status       - Show services status"

# Development environment
dev:
	@echo "🚀 Starting ChartGenius v2.0 development environment..."
	@if [ ! -f .env ]; then \
		echo "⚠️  .env file not found. Creating from .env.example..."; \
		cp .env.example .env; \
		echo "📝 Please edit .env file with your configuration"; \
		exit 1; \
	fi
	@echo "📦 Installing backend dependencies..."
	cd backend && pip install -r requirements.txt
	@echo "📦 Installing frontend dependencies..."
	cd frontend && npm install
	@echo "🔧 Starting Redis..."
	docker-compose up -d redis
	@echo "🌐 Starting backend server..."
	cd backend && python -m uvicorn app:app --reload --host 0.0.0.0 --port 8000 &
	@echo "⚛️  Starting frontend server..."
	cd frontend && npm run dev &
	@echo "🤖 Starting Telegram bot..."
	cd bot && python bot.py &
	@echo "✅ Development environment started!"
	@echo "   Backend:  http://localhost:8000"
	@echo "   Frontend: http://localhost:5173"
	@echo "   Redis:    localhost:6379"

# Production environment
prod:
	@echo "🏭 Starting production environment..."
	docker-compose up -d --build

# Install dependencies
install:
	@echo "📦 Installing all dependencies..."
	@echo "Installing backend dependencies..."
	cd backend && pip install -r requirements.txt
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "✅ All dependencies installed!"

# Clean temporary files
clean:
	@echo "🧹 Cleaning temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name ".pytest_cache" -delete
	find . -type d -name "node_modules" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.log" -delete
	@echo "✅ Cleanup completed!"

# Database migration
migrate:
	@echo "🔄 Starting Firestore to Oracle AJD migration..."
	cd backend && python migrations/firestore_to_oracle.py

# Setup Oracle AJD
db-setup:
	@echo "🗄️  Setting up Oracle AJD tables..."
	cd backend && python -c "from services.oracle_client import oracle_client; print('Oracle AJD setup completed!')"

# Test Oracle connection
test-oracle:
	@echo "🔍 Testing Oracle AJD connection..."
	python scripts/test-oracle-connection.py

# Setup Gemini CLI
setup-gemini:
	@echo "🤖 Setting up Gemini CLI..."
	bash scripts/setup-gemini-cli.sh

# Test LLM providers
test-llm:
	@echo "🧠 Testing LLM providers..."
	curl -s http://localhost:8000/api/config/llm/test || echo "Backend not running"

# Run tests
test:
	@echo "🧪 Running tests..."
	cd backend && python -m pytest tests/ -v
	cd frontend && npm test
	@echo "✅ All tests completed!"

# Run linting
lint:
	@echo "🔍 Running linting..."
	cd backend && python -m flake8 . --max-line-length=120 --exclude=migrations
	cd frontend && npm run lint
	@echo "✅ Linting completed!"

# Deploy to OCI
deploy:
	@echo "🚀 Deploying to Oracle Cloud Infrastructure..."
	@if [ ! -f terraform/terraform.tfvars ]; then \
		echo "⚠️  terraform.tfvars not found. Please configure Terraform first."; \
		exit 1; \
	fi
	cd terraform && terraform plan
	cd terraform && terraform apply -auto-approve
	@echo "✅ Deployment completed!"

# Show logs
logs:
	@echo "📋 Showing application logs..."
	docker-compose logs -f --tail=100

# Show status
status:
	@echo "📊 Services status:"
	@echo ""
	@echo "Docker containers:"
	docker-compose ps
	@echo ""
	@echo "Redis status:"
	@docker-compose exec redis redis-cli ping 2>/dev/null || echo "Redis not running"
	@echo ""
	@echo "Backend health:"
	@curl -s http://localhost:8000/health 2>/dev/null || echo "Backend not responding"

# Stop all services
stop:
	@echo "🛑 Stopping all services..."
	docker-compose down
	@pkill -f "uvicorn app:app" 2>/dev/null || true
	@pkill -f "npm run dev" 2>/dev/null || true
	@pkill -f "python bot.py" 2>/dev/null || true
	@echo "✅ All services stopped!"

# Restart services
restart: stop dev

# Update dependencies
update:
	@echo "🔄 Updating dependencies..."
	cd backend && pip install -r requirements.txt --upgrade
	cd frontend && npm update
	@echo "✅ Dependencies updated!"

# Generate API documentation
docs:
	@echo "📚 Generating API documentation..."
	cd backend && python -c "import app; print('API docs available at http://localhost:8000/docs')"

# Backup database
backup:
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	@echo "Backup created: backups/backup_$(shell date +%Y%m%d_%H%M%S).json"

# Environment setup for new developers
setup:
	@echo "🔧 Setting up development environment for new developer..."
	@echo "1. Copying environment template..."
	@cp .env.example .env
	@echo "2. Installing dependencies..."
	@make install
	@echo "3. Setting up pre-commit hooks..."
	@echo "✅ Setup completed!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Edit .env file with your configuration"
	@echo "2. Setup Oracle Cloud account and AJD"
	@echo "3. Run 'make dev' to start development"

# Check system requirements
check:
	@echo "🔍 Checking system requirements..."
	@python --version || echo "❌ Python not found"
	@node --version || echo "❌ Node.js not found"
	@docker --version || echo "❌ Docker not found"
	@docker-compose --version || echo "❌ Docker Compose not found"
	@echo "✅ System check completed!"
