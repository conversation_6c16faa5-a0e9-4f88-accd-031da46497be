#!/usr/bin/env python3

"""
ChartGenius v2.0 - Oracle AJD Connection Test Script
"""

import os
import sys
import json
from datetime import datetime

# Добавляем путь к backend для импортов
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

def test_oracle_connection():
    """Тестирование подключения к Oracle AJD"""
    
    print("🔍 Testing Oracle Autonomous JSON Database connection...")
    print("=" * 60)
    
    try:
        # Импортируем Oracle клиент
        from services.oracle_client import oracle_client
        
        print("✅ Oracle client imported successfully")
        
        # Тестовые данные
        test_doc_id = "test_connection_" + datetime.now().strftime("%Y%m%d_%H%M%S")
        test_data = {
            "test": True,
            "timestamp": datetime.now().isoformat(),
            "message": "Oracle AJD connection test",
            "version": "2.0.0"
        }
        
        print(f"📝 Testing document operations with ID: {test_doc_id}")
        
        # Тест 1: Вставка документа
        print("\n1. Testing document insertion...")
        success = oracle_client.insert_document('config', test_doc_id, test_data)
        if success:
            print("   ✅ Document inserted successfully")
        else:
            print("   ❌ Document insertion failed")
            return False
        
        # Тест 2: Получение документа
        print("\n2. Testing document retrieval...")
        retrieved_data = oracle_client.get_document('config', test_doc_id)
        if retrieved_data:
            print("   ✅ Document retrieved successfully")
            print(f"   📄 Retrieved data: {json.dumps(retrieved_data, indent=2)}")
            
            # Проверяем соответствие данных
            if retrieved_data.get('test') == test_data['test']:
                print("   ✅ Data integrity verified")
            else:
                print("   ❌ Data integrity check failed")
                return False
        else:
            print("   ❌ Document retrieval failed")
            return False
        
        # Тест 3: Обновление документа
        print("\n3. Testing document update...")
        updated_data = test_data.copy()
        updated_data['updated'] = True
        updated_data['update_timestamp'] = datetime.now().isoformat()
        
        success = oracle_client.update_document('config', test_doc_id, updated_data)
        if success:
            print("   ✅ Document updated successfully")
            
            # Проверяем обновление
            updated_retrieved = oracle_client.get_document('config', test_doc_id)
            if updated_retrieved and updated_retrieved.get('updated'):
                print("   ✅ Update verification successful")
            else:
                print("   ❌ Update verification failed")
                return False
        else:
            print("   ❌ Document update failed")
            return False
        
        # Тест 4: Запрос документов
        print("\n4. Testing document query...")
        query_filter = {"test": True}
        results = oracle_client.query_documents('config', query_filter, limit=10)
        if results:
            print(f"   ✅ Query successful, found {len(results)} documents")
            
            # Проверяем, что наш тестовый документ в результатах
            found_test_doc = any(doc.get('_id') == test_doc_id for doc in results)
            if found_test_doc:
                print("   ✅ Test document found in query results")
            else:
                print("   ❌ Test document not found in query results")
        else:
            print("   ❌ Query failed or returned no results")
        
        # Тест 5: Удаление документа
        print("\n5. Testing document deletion...")
        success = oracle_client.delete_document('config', test_doc_id)
        if success:
            print("   ✅ Document deleted successfully")
            
            # Проверяем удаление
            deleted_check = oracle_client.get_document('config', test_doc_id)
            if not deleted_check:
                print("   ✅ Deletion verification successful")
            else:
                print("   ❌ Deletion verification failed - document still exists")
                return False
        else:
            print("   ❌ Document deletion failed")
            return False
        
        print("\n🎉 All Oracle AJD tests passed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure Oracle client dependencies are installed:")
        print("   pip install oracledb")
        return False
        
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        print("\nTroubleshooting:")
        print("1. Check your Oracle AJD credentials in .env file:")
        print("   - ORACLE_USERNAME")
        print("   - ORACLE_PASSWORD") 
        print("   - ORACLE_DSN")
        print("2. Verify Oracle AJD instance is running")
        print("3. Check network connectivity to Oracle Cloud")
        return False

def test_environment_variables():
    """Проверка переменных окружения"""
    
    print("\n🔧 Checking environment variables...")
    print("-" * 40)
    
    required_vars = [
        'ORACLE_USERNAME',
        'ORACLE_PASSWORD',
        'ORACLE_DSN'
    ]
    
    optional_vars = [
        'ORACLE_WALLET_LOCATION',
        'ORACLE_WALLET_PASSWORD',
        'ORACLE_POOL_MIN',
        'ORACLE_POOL_MAX'
    ]
    
    missing_required = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"❌ {var}: Not set")
            missing_required.append(var)
    
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"ℹ️  {var}: {value}")
        else:
            print(f"ℹ️  {var}: Not set (optional)")
    
    if missing_required:
        print(f"\n❌ Missing required environment variables: {', '.join(missing_required)}")
        print("Please add them to your .env file")
        return False
    
    print("\n✅ All required environment variables are set")
    return True

def main():
    """Основная функция"""
    
    print("🚀 ChartGenius v2.0 - Oracle AJD Connection Test")
    print("=" * 60)
    
    # Загружаем переменные окружения
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded from .env")
    except ImportError:
        print("⚠️  python-dotenv not installed, using system environment variables")
    except Exception as e:
        print(f"⚠️  Error loading .env file: {e}")
    
    # Проверяем переменные окружения
    if not test_environment_variables():
        print("\n❌ Environment check failed. Please fix configuration and try again.")
        sys.exit(1)
    
    # Тестируем подключение к Oracle
    if test_oracle_connection():
        print("\n🎉 Oracle AJD connection test completed successfully!")
        print("\nYour Oracle Autonomous JSON Database is ready for ChartGenius v2.0!")
        sys.exit(0)
    else:
        print("\n❌ Oracle AJD connection test failed!")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
