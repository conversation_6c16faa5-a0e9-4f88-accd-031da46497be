#!/bin/bash

# ChartGenius v2.0 - Gemini CLI Setup Script

set -e

echo "🤖 Setting up Google Gemini CLI for ChartGenius v2.0"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if running on Windows (Git Bash, WSL, etc.)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    IS_WINDOWS=true
    print_warning "Detected Windows environment"
else
    IS_WINDOWS=false
fi

print_step "Checking prerequisites..."

# Check Python
if command_exists python; then
    PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
    print_status "Python found: $PYTHON_VERSION"
elif command_exists python3; then
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_status "Python3 found: $PYTHON_VERSION"
    alias python=python3
else
    print_error "Python not found. Please install Python 3.8+"
    exit 1
fi

# Check pip
if command_exists pip; then
    print_status "pip found"
elif command_exists pip3; then
    print_status "pip3 found"
    alias pip=pip3
else
    print_error "pip not found. Please install pip"
    exit 1
fi

print_step "Installing Google Cloud SDK..."

# Check if gcloud is already installed
if command_exists gcloud; then
    GCLOUD_VERSION=$(gcloud version --format="value(Google Cloud SDK)" 2>/dev/null || echo "unknown")
    print_status "Google Cloud SDK already installed: $GCLOUD_VERSION"
    
    # Update gcloud
    print_status "Updating Google Cloud SDK..."
    gcloud components update --quiet
else
    print_status "Installing Google Cloud SDK..."
    
    if [ "$IS_WINDOWS" = true ]; then
        print_warning "Please install Google Cloud SDK manually on Windows:"
        print_warning "1. Download from: https://cloud.google.com/sdk/docs/install"
        print_warning "2. Run the installer"
        print_warning "3. Restart your terminal"
        print_warning "4. Run this script again"
        exit 1
    else
        # Install gcloud on Linux/macOS
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            if command_exists brew; then
                brew install --cask google-cloud-sdk
            else
                print_error "Homebrew not found. Please install Google Cloud SDK manually:"
                print_error "https://cloud.google.com/sdk/docs/install"
                exit 1
            fi
        else
            # Linux
            curl https://sdk.cloud.google.com | bash
            exec -l $SHELL
        fi
    fi
fi

print_step "Installing Google Generative AI Python library..."

# Install google-generativeai
pip install --upgrade google-generativeai google-auth google-auth-oauthlib google-auth-httplib2

print_status "Google Generative AI library installed"

print_step "Setting up authentication..."

# Check if user is already authenticated
if gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    ACTIVE_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
    print_status "Already authenticated with account: $ACTIVE_ACCOUNT"
    
    # Check if application default credentials are set
    if gcloud auth application-default print-access-token >/dev/null 2>&1; then
        print_status "Application Default Credentials are already configured"
    else
        print_warning "Application Default Credentials not found"
        print_status "Setting up Application Default Credentials..."
        gcloud auth application-default login
    fi
else
    print_status "Setting up Google Cloud authentication..."
    
    # Login to Google Cloud
    print_status "Opening browser for Google Cloud authentication..."
    gcloud auth login
    
    # Set up application default credentials
    print_status "Setting up Application Default Credentials..."
    gcloud auth application-default login
fi

print_step "Configuring project..."

# Check if project is set
CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
if [ -z "$CURRENT_PROJECT" ]; then
    print_warning "No default project set"
    print_status "Listing available projects..."
    gcloud projects list
    
    echo ""
    read -p "Enter your Google Cloud Project ID: " PROJECT_ID
    
    if [ -n "$PROJECT_ID" ]; then
        gcloud config set project "$PROJECT_ID"
        print_status "Project set to: $PROJECT_ID"
    else
        print_warning "No project set. You can set it later with: gcloud config set project YOUR_PROJECT_ID"
    fi
else
    print_status "Current project: $CURRENT_PROJECT"
fi

print_step "Testing Gemini API access..."

# Test Python script
cat > test_gemini.py << 'EOF'
import google.generativeai as genai
import google.auth

try:
    # Use Application Default Credentials
    credentials, project = google.auth.default()
    genai.configure(credentials=credentials)
    
    # Test with a simple model
    model = genai.GenerativeModel('gemini-2.0-flash-exp')
    response = model.generate_content("Say 'Hello from Gemini!'")
    
    print("✅ Gemini API test successful!")
    print(f"Response: {response.text}")
    print(f"Project: {project}")
    
except Exception as e:
    print(f"❌ Gemini API test failed: {e}")
    print("Please check your authentication and project settings")
EOF

print_status "Running Gemini API test..."
python test_gemini.py

# Clean up test file
rm test_gemini.py

print_step "Updating ChartGenius configuration..."

# Update .env file if it exists
if [ -f .env ]; then
    # Check if GOOGLE_CLOUD_PROJECT is already set
    if grep -q "GOOGLE_CLOUD_PROJECT=" .env; then
        print_status "GOOGLE_CLOUD_PROJECT already configured in .env"
    else
        CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
        if [ -n "$CURRENT_PROJECT" ]; then
            echo "" >> .env
            echo "# Google Cloud Configuration" >> .env
            echo "GOOGLE_CLOUD_PROJECT=$CURRENT_PROJECT" >> .env
            print_status "Added GOOGLE_CLOUD_PROJECT to .env file"
        fi
    fi
    
    # Set default LLM provider to gemini if not set
    if ! grep -q "DEFAULT_LLM_PROVIDER=" .env; then
        echo "DEFAULT_LLM_PROVIDER=gemini" >> .env
        print_status "Set default LLM provider to Gemini"
    fi
else
    print_warning ".env file not found. Please create it from .env.example"
fi

print_step "Gemini CLI setup completed!"

echo ""
echo "🎉 Google Gemini CLI is now configured for ChartGenius v2.0!"
echo ""
echo "Configuration summary:"
echo "  ✅ Google Cloud SDK installed and configured"
echo "  ✅ Application Default Credentials set up"
echo "  ✅ Google Generative AI library installed"
echo "  ✅ Gemini API access tested"
echo ""
echo "Next steps:"
echo "1. Start ChartGenius development server: make dev"
echo "2. Test LLM switching: POST /api/config/llm with provider='gemini'"
echo "3. Check LLM status: GET /api/config/llm/status"
echo ""
echo "Useful commands:"
echo "  gcloud auth list                    - List authenticated accounts"
echo "  gcloud config get-value project     - Show current project"
echo "  gcloud auth application-default login - Re-authenticate if needed"
echo ""
print_status "Happy coding with Gemini! 🤖"
