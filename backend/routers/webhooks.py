# backend/routers/webhooks.py

import logging
from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, Header
from datetime import datetime

from bot.services.wallet_pay_client import get_wallet_pay_client
from backend.services.oracle_client import oracle_client
from backend.config.config import logger

router = APIRouter(prefix="/api/webhooks", tags=["webhooks"])

@router.post("/wallet-pay")
async def wallet_pay_webhook(
    request: Request,
    wpay_store_api_signature: str = Header(None, alias="wpay-store-api-signature"),
    wpay_store_api_timestamp: str = Header(None, alias="wpay-store-api-timestamp")
):
    """
    Webhook для обработки событий Wallet Pay

    Args:
        request: HTTP запрос с данными от Wallet Pay
        wpay_store_api_signature: Подпись Wallet Pay для верификации
        wpay_store_api_timestamp: Временная метка

    Returns:
        Подтверждение обработки webhook
    """
    try:
        # Получаем тело запроса
        payload = await request.body()
        body_str = payload.decode()

        if not wpay_store_api_signature or not wpay_store_api_timestamp:
            raise HTTPException(status_code=400, detail="Missing Wallet Pay signature or timestamp")

        # Получаем Wallet Pay клиент
        wallet_pay_client = get_wallet_pay_client()

        # Верифицируем подпись
        if not wallet_pay_client.verify_webhook_signature(body_str, wpay_store_api_signature, wpay_store_api_timestamp):
            raise HTTPException(status_code=400, detail="Invalid webhook signature")

        # Парсим данные
        webhook_data = json.loads(body_str)
        event_type = webhook_data.get('eventType')
        order_data = webhook_data.get('eventData', {})

        logger.info(f"Получен Wallet Pay webhook: {event_type}")

        if event_type == 'ORDER_PAID':
            await handle_wallet_pay_order_paid(order_data)
        elif event_type == 'ORDER_FAILED':
            await handle_wallet_pay_order_failed(order_data)
        else:
            logger.info(f"Необработанный тип события Wallet Pay: {event_type}")

        return {"success": True, "event_type": event_type}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка обработки Wallet Pay webhook: {e}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")

async def handle_wallet_pay_order_paid(order_data: Dict[str, Any]):
    """Обработка оплаченного заказа Wallet Pay"""
    try:
        order_id = order_data.get('id')
        external_id = order_data.get('externalId')
        amount = order_data.get('orderAmount', {})

        logger.info(f"Wallet Pay заказ оплачен: {order_id}, external_id: {external_id}")

        # Получаем информацию о платеже из базы данных
        payment_data = oracle_client.get_document('payments', external_id)

        if not payment_data:
            logger.warning(f"Платеж не найден для external_id {external_id}")
            return

        # Обновляем статус платежа
        payment_data.update({
            'status': 'completed',
            'wallet_pay_order_data': order_data,
            'completed_at': datetime.utcnow().isoformat(),
            'paid_amount': amount.get('amount'),
            'paid_currency': amount.get('currencyCode')
        })

        oracle_client.update_document('payments', external_id, payment_data)

        # Активируем подписку
        user_id = payment_data.get('user_id')
        plan_id = payment_data.get('plan_id')

        if user_id and plan_id:
            # Импортируем обработчик платежей
            from bot.handlers.payments import get_payment_handler
            payment_handler = get_payment_handler()

            success = await payment_handler.handle_successful_payment(
                user_id, plan_id, 'wallet_pay'
            )

            if success:
                logger.info(f"Подписка {plan_id} активирована для пользователя {user_id}")
            else:
                logger.error(f"Ошибка активации подписки {plan_id} для пользователя {user_id}")

        logger.info(f"Wallet Pay заказ обработан: {external_id}")

    except Exception as e:
        logger.error(f"Ошибка обработки Wallet Pay заказа: {e}")

async def handle_wallet_pay_order_failed(order_data: Dict[str, Any]):
    """Обработка неудачного заказа Wallet Pay"""
    try:
        order_id = order_data.get('id')
        external_id = order_data.get('externalId')

        logger.warning(f"Wallet Pay заказ неудачен: {order_id}, external_id: {external_id}")

        # Получаем информацию о платеже из базы данных
        payment_data = oracle_client.get_document('payments', external_id)

        if payment_data:
            # Обновляем статус платежа
            payment_data.update({
                'status': 'failed',
                'wallet_pay_order_data': order_data,
                'failed_at': datetime.utcnow().isoformat()
            })

            oracle_client.update_document('payments', external_id, payment_data)

        logger.info(f"Wallet Pay неудачный заказ обработан: {external_id}")

    except Exception as e:
        logger.error(f"Ошибка обработки неудачного Wallet Pay заказа: {e}")

# Старые Stripe обработчики удалены - заменены на Wallet Pay

@router.get("/wallet-pay/test")
async def test_wallet_pay_webhook():
    """Тестовый endpoint для проверки Wallet Pay webhook"""
    try:
        wallet_pay_client = get_wallet_pay_client()

        if not wallet_pay_client.is_configured():
            return {
                "success": False,
                "error": "Wallet Pay not configured"
            }

        balance = await wallet_pay_client.get_balance()

        return {
            "success": True,
            "message": "Wallet Pay webhook endpoint is working",
            "balance": balance,
            "supported_currencies": wallet_pay_client.get_supported_currencies()
        }

    except Exception as e:
        logger.error(f"Ошибка тестирования Wallet Pay webhook: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/telegram/payments")
async def telegram_payments_webhook(request: Request):
    """
    Webhook для обработки Telegram Payments (если нужен)
    
    Args:
        request: HTTP запрос с данными от Telegram
        
    Returns:
        Подтверждение обработки webhook
    """
    try:
        # Получаем данные от Telegram
        data = await request.json()
        
        logger.info(f"Получен Telegram payments webhook: {data}")
        
        # Здесь можно добавить дополнительную логику обработки
        # Telegram Payments обычно обрабатываются через bot handlers
        
        return {"success": True}
        
    except Exception as e:
        logger.error(f"Ошибка обработки Telegram payments webhook: {e}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")
