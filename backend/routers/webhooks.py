# backend/routers/webhooks.py

import logging
from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, Header
from datetime import datetime

from bot.services.stripe_client import get_stripe_client
from backend.services.oracle_client import oracle_client
from backend.config.config import logger

router = APIRouter(prefix="/api/webhooks", tags=["webhooks"])

@router.post("/stripe")
async def stripe_webhook(
    request: Request,
    stripe_signature: str = Header(None, alias="stripe-signature")
):
    """
    Webhook для обработки событий Stripe
    
    Args:
        request: HTTP запрос с данными от Stripe
        stripe_signature: Подпись Stripe для верификации
        
    Returns:
        Подтверждение обработки webhook
    """
    try:
        # Получаем тело запроса
        payload = await request.body()
        
        if not stripe_signature:
            raise HTTPException(status_code=400, detail="Missing Stripe signature")
        
        # Получаем Stripe клиент
        stripe_client = get_stripe_client()
        
        # Обрабатываем webhook
        event = stripe_client.handle_webhook(payload.decode(), stripe_signature)
        
        if not event:
            raise HTTPException(status_code=400, detail="Invalid webhook")
        
        # Обрабатываем различные типы событий
        event_type = event['type']
        event_data = event['data']['object']
        
        logger.info(f"Получен Stripe webhook: {event_type}")
        
        if event_type == 'checkout.session.completed':
            await handle_checkout_completed(event_data)
        elif event_type == 'payment_intent.succeeded':
            await handle_payment_succeeded(event_data)
        elif event_type == 'payment_intent.payment_failed':
            await handle_payment_failed(event_data)
        elif event_type == 'invoice.payment_succeeded':
            await handle_invoice_payment_succeeded(event_data)
        elif event_type == 'customer.subscription.created':
            await handle_subscription_created(event_data)
        elif event_type == 'customer.subscription.updated':
            await handle_subscription_updated(event_data)
        elif event_type == 'customer.subscription.deleted':
            await handle_subscription_deleted(event_data)
        else:
            logger.info(f"Необработанный тип события Stripe: {event_type}")
        
        return {"success": True, "event_type": event_type}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка обработки Stripe webhook: {e}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")

async def handle_checkout_completed(session_data: Dict[str, Any]):
    """Обработка завершенной checkout сессии"""
    try:
        session_id = session_data['id']
        payment_status = session_data['payment_status']
        metadata = session_data.get('metadata', {})
        
        # Получаем информацию о платеже из базы данных
        payment_data = oracle_client.get_document('payments', session_id)
        
        if not payment_data:
            logger.warning(f"Платеж не найден для сессии {session_id}")
            return
        
        # Обновляем статус платежа
        payment_data.update({
            'status': 'completed' if payment_status == 'paid' else 'failed',
            'stripe_session_data': session_data,
            'completed_at': datetime.utcnow().isoformat()
        })
        
        oracle_client.update_document('payments', session_id, payment_data)
        
        # Если платеж успешен, активируем подписку
        if payment_status == 'paid':
            user_id = metadata.get('user_id')
            plan_id = metadata.get('plan_id')
            
            if user_id and plan_id:
                # Импортируем обработчик платежей
                from bot.handlers.payments import get_payment_handler
                payment_handler = get_payment_handler("", "")  # Dummy keys
                
                success = await payment_handler.handle_successful_payment(
                    user_id, plan_id, 'stripe'
                )
                
                if success:
                    logger.info(f"Подписка {plan_id} активирована для пользователя {user_id}")
                else:
                    logger.error(f"Ошибка активации подписки {plan_id} для пользователя {user_id}")
        
        logger.info(f"Checkout сессия обработана: {session_id}")
        
    except Exception as e:
        logger.error(f"Ошибка обработки checkout сессии: {e}")

async def handle_payment_succeeded(payment_intent_data: Dict[str, Any]):
    """Обработка успешного платежа"""
    try:
        payment_intent_id = payment_intent_data['id']
        amount = payment_intent_data['amount']
        currency = payment_intent_data['currency']
        metadata = payment_intent_data.get('metadata', {})
        
        logger.info(f"Платеж успешен: {payment_intent_id}, сумма: {amount} {currency}")
        
        # Дополнительная логика обработки успешного платежа
        # Например, отправка уведомлений, обновление статистики и т.д.
        
    except Exception as e:
        logger.error(f"Ошибка обработки успешного платежа: {e}")

async def handle_payment_failed(payment_intent_data: Dict[str, Any]):
    """Обработка неудачного платежа"""
    try:
        payment_intent_id = payment_intent_data['id']
        last_payment_error = payment_intent_data.get('last_payment_error', {})
        
        logger.warning(f"Платеж неудачен: {payment_intent_id}, ошибка: {last_payment_error}")
        
        # Дополнительная логика обработки неудачного платежа
        # Например, уведомление пользователя, попытка повторного платежа и т.д.
        
    except Exception as e:
        logger.error(f"Ошибка обработки неудачного платежа: {e}")

async def handle_invoice_payment_succeeded(invoice_data: Dict[str, Any]):
    """Обработка успешной оплаты инвойса (для подписок)"""
    try:
        invoice_id = invoice_data['id']
        subscription_id = invoice_data.get('subscription')
        customer_id = invoice_data.get('customer')
        
        logger.info(f"Инвойс оплачен: {invoice_id}, подписка: {subscription_id}")
        
        # Логика обработки оплаты подписки
        
    except Exception as e:
        logger.error(f"Ошибка обработки оплаты инвойса: {e}")

async def handle_subscription_created(subscription_data: Dict[str, Any]):
    """Обработка создания подписки"""
    try:
        subscription_id = subscription_data['id']
        customer_id = subscription_data['customer']
        status = subscription_data['status']
        
        logger.info(f"Подписка создана: {subscription_id}, статус: {status}")
        
        # Логика обработки новой подписки
        
    except Exception as e:
        logger.error(f"Ошибка обработки создания подписки: {e}")

async def handle_subscription_updated(subscription_data: Dict[str, Any]):
    """Обработка обновления подписки"""
    try:
        subscription_id = subscription_data['id']
        status = subscription_data['status']
        
        logger.info(f"Подписка обновлена: {subscription_id}, статус: {status}")
        
        # Логика обработки обновления подписки
        
    except Exception as e:
        logger.error(f"Ошибка обработки обновления подписки: {e}")

async def handle_subscription_deleted(subscription_data: Dict[str, Any]):
    """Обработка удаления подписки"""
    try:
        subscription_id = subscription_data['id']
        
        logger.info(f"Подписка удалена: {subscription_id}")
        
        # Логика обработки удаления подписки
        
    except Exception as e:
        logger.error(f"Ошибка обработки удаления подписки: {e}")

@router.get("/stripe/test")
async def test_stripe_webhook():
    """Тестовый endpoint для проверки Stripe webhook"""
    try:
        stripe_client = get_stripe_client()
        balance = stripe_client.get_balance()
        
        return {
            "success": True,
            "message": "Stripe webhook endpoint is working",
            "balance": balance
        }
        
    except Exception as e:
        logger.error(f"Ошибка тестирования Stripe webhook: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/telegram/payments")
async def telegram_payments_webhook(request: Request):
    """
    Webhook для обработки Telegram Payments (если нужен)
    
    Args:
        request: HTTP запрос с данными от Telegram
        
    Returns:
        Подтверждение обработки webhook
    """
    try:
        # Получаем данные от Telegram
        data = await request.json()
        
        logger.info(f"Получен Telegram payments webhook: {data}")
        
        # Здесь можно добавить дополнительную логику обработки
        # Telegram Payments обычно обрабатываются через bot handlers
        
        return {"success": True}
        
    except Exception as e:
        logger.error(f"Ошибка обработки Telegram payments webhook: {e}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")
