{"name": "<PERSON><PERSON><PERSON>-frontend", "version": "2.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@reduxjs/toolkit": "^2.8.2", "@telegram-apps/sdk": "^2.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lightweight-charts": "^5.0.7", "lucide-react": "^0.468.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.1.2", "react-router-dom": "^7.6.2", "redux": "^5.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.17.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.1.0", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "jsdom": "^25.0.1", "postcss": "^8.5.3", "tailwindcss": "^4.0.0", "terser": "^5.36.0", "typescript": "^5.7.2", "vite": "^6.0.0", "vitest": "^3.2.2"}}