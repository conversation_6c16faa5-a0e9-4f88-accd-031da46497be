<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="90" fill="url(#grad1)" stroke="#3b82f6" stroke-width="2" opacity="0.3"/>
  
  <!-- Chart icon -->
  <path d="M50 150 L80 120 L110 100 L140 80 L170 50" stroke="#3b82f6" stroke-width="3" fill="none" opacity="0.5"/>
  <circle cx="50" cy="150" r="4" fill="#3b82f6" opacity="0.6"/>
  <circle cx="80" cy="120" r="4" fill="#3b82f6" opacity="0.6"/>
  <circle cx="110" cy="100" r="4" fill="#3b82f6" opacity="0.6"/>
  <circle cx="140" cy="80" r="4" fill="#3b82f6" opacity="0.6"/>
  <circle cx="170" cy="50" r="4" fill="#3b82f6" opacity="0.6"/>
  
  <!-- Text -->
  <text x="100" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#3b82f6" opacity="0.4">ChartGenius</text>
</svg>
