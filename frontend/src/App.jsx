import React from 'react'
import './index.css'

export default function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-blue-600">ChartGenius</h1>
              </div>
              <div className="ml-4">
                <span className="text-sm text-gray-500">v2.0</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-green-600">● AI Ready</span>
              <span className="text-sm text-blue-600">● Live Data</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold mb-4">Market Data</h2>
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-gray-500">BTCUSDT</div>
                  <div className="text-xl font-bold">$43,250.50</div>
                  <div className="text-sm text-green-600">+2.45%</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Volume</div>
                  <div className="text-lg">2.4B</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">RSI</div>
                  <div className="text-lg">65.2</div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6 mt-6">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="space-y-2">
                <button className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                  Generate Analysis
                </button>
                <button className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded hover:bg-gray-300">
                  Add to Watchlist
                </button>
              </div>
            </div>
          </div>

          {/* Chart Area */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">BTCUSDT Chart</h2>
                <div className="flex space-x-2">
                  <button className="px-3 py-1 bg-blue-600 text-white rounded text-sm">1H</button>
                  <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">4H</button>
                  <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">1D</button>
                </div>
              </div>

              {/* Chart Placeholder */}
              <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center relative">
                {/* Watermark */}
                <div
                  className="absolute inset-0 opacity-10 bg-no-repeat bg-center bg-contain"
                  style={{backgroundImage: "url('/371.svg')"}}
                ></div>

                <div className="text-center z-10">
                  <div className="text-4xl mb-4">📈</div>
                  <h3 className="text-xl font-semibold text-gray-700">Trading Chart</h3>
                  <p className="text-gray-500 mt-2">Interactive chart will be displayed here</p>
                  <p className="text-sm text-gray-400 mt-1">TradingView widget integration</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">AI Analysis</h3>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg relative">
                  {/* Watermark for analysis */}
                  <div
                    className="absolute inset-0 opacity-5 bg-no-repeat bg-center bg-contain"
                    style={{backgroundImage: "url('/371.svg')"}}
                  ></div>
                  <div className="relative z-10">
                    <h4 className="font-medium text-blue-800">Technical Analysis</h4>
                    <p className="text-sm text-blue-600 mt-1">
                      Bitcoin shows strong bullish momentum with price breaking above the 20-day moving average.
                    </p>
                  </div>
                </div>

                <div className="p-4 bg-green-50 rounded-lg relative">
                  <div
                    className="absolute inset-0 opacity-5 bg-no-repeat bg-center bg-contain"
                    style={{backgroundImage: "url('/371.svg')"}}
                  ></div>
                  <div className="relative z-10">
                    <h4 className="font-medium text-green-800">Support/Resistance</h4>
                    <p className="text-sm text-green-600 mt-1">
                      Support: $42,800 | Resistance: $44,000
                    </p>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 rounded-lg relative">
                  <div
                    className="absolute inset-0 opacity-5 bg-no-repeat bg-center bg-contain"
                    style={{backgroundImage: "url('/371.svg')"}}
                  ></div>
                  <div className="relative z-10">
                    <h4 className="font-medium text-yellow-800">Recommendation</h4>
                    <p className="text-sm text-yellow-600 mt-1">
                      Consider long positions on pullbacks to support levels.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6 mt-6">
              <h3 className="text-lg font-semibold mb-4">System Status</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">LLM Provider</span>
                  <span className="text-sm text-green-600">Gemini 2.5 Pro</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Database</span>
                  <span className="text-sm text-green-600">Oracle AJD</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Cache</span>
                  <span className="text-sm text-green-600">Redis</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-500">
            ChartGenius v2.0 - Powered by AI • Real-time market analysis
          </div>
        </div>
      </footer>
    </div>
  )
}
