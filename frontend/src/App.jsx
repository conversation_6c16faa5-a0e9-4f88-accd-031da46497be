import React, { useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { useDarkMode } from './hooks/useDarkMode'
import { isTelegramWebApp, isMobile } from './lib/utils'
import { DesktopLayout } from './layouts/DesktopLayout'
import { TelegramLayout } from './layouts/TelegramLayout'
import { ChartPage } from './pages/ChartPage'
import { AnalysisPage } from './pages/AnalysisPage'
import { WatchlistPage } from './pages/WatchlistPage'
import { SettingsPage } from './pages/SettingsPage'
import './index.css'

export default function App() {
  const { isDark } = useDarkMode()

  // Apply theme class to document
  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [isDark])

  // Determine layout based on environment
  const shouldUseTelegramLayout = isTelegramWebApp() || isMobile()

  // Layout wrapper component
  const LayoutWrapper = ({ children }) => {
    if (shouldUseTelegramLayout) {
      return <TelegramLayout>{children}</TelegramLayout>
    }
    return <DesktopLayout>{children}</DesktopLayout>
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Routes>
        <Route
          path="/"
          element={
            <LayoutWrapper>
              <ChartPage />
            </LayoutWrapper>
          }
        />
        <Route
          path="/analysis"
          element={
            <LayoutWrapper>
              <AnalysisPage />
            </LayoutWrapper>
          }
        />
        <Route
          path="/watchlist"
          element={
            <LayoutWrapper>
              <WatchlistPage />
            </LayoutWrapper>
          }
        />
        <Route
          path="/settings"
          element={
            <LayoutWrapper>
              <SettingsPage />
            </LayoutWrapper>
          }
        />
      </Routes>
    </div>
  )
}
