import React, { useState, useEffect } from 'react'
import { cn } from '../lib/utils'
import { useTelegramWebApp } from '../hooks/useTelegramWebApp'
import { TopBarMobile } from '../components/mobile/TopBarMobile'
import { KPIStack } from '../components/mobile/KPIStack'
import { BottomTabs } from '../components/mobile/BottomTabs'
import { ChartWatermark } from '../components/ui/Watermark'

/**
 * Telegram WebApp Layout for ChartGenius v2.0
 * Layout: TopBar + KPI Stack + Chart + Bottom Tabs
 * Optimized for mobile devices and Telegram WebApp
 */
export const TelegramLayout = ({ children }) => {
  const [activeTab, setActiveTab] = useState('chart')
  const [isExpanded, setIsExpanded] = useState(false)
  const { 
    isReady, 
    isTelegramWebApp, 
    user, 
    theme, 
    viewportHeight,
    showMainButton,
    hideMainButton,
    hapticFeedback 
  } = useTelegramWebApp()

  // Adjust layout based on Telegram WebApp viewport
  useEffect(() => {
    if (isTelegramWebApp && isReady) {
      // Configure main button based on current tab
      if (activeTab === 'chart') {
        showMainButton('Analyze', () => {
          hapticFeedback('impact', 'medium')
          // Trigger analysis
        })
      } else {
        hideMainButton()
      }
    }

    return () => {
      if (isTelegramWebApp) {
        hideMainButton()
      }
    }
  }, [activeTab, isTelegramWebApp, isReady, showMainButton, hideMainButton, hapticFeedback])

  // Handle tab change with haptic feedback
  const handleTabChange = (tab) => {
    if (isTelegramWebApp) {
      hapticFeedback('selection')
    }
    setActiveTab(tab)
  }

  // Calculate dynamic heights for mobile optimization
  const topBarHeight = 56 // px
  const kpiStackHeight = isExpanded ? 120 : 80 // px
  const bottomTabsHeight = 60 // px
  const availableHeight = viewportHeight - topBarHeight - kpiStackHeight - bottomTabsHeight

  return (
    <div 
      className={cn(
        "flex flex-col bg-background twa-viewport",
        theme === 'dark' ? 'dark' : ''
      )}
      style={{ height: isTelegramWebApp ? `${viewportHeight}px` : '100vh' }}
    >
      {/* Top Bar Mobile */}
      <div 
        className="flex-shrink-0 border-b border-border bg-card/95 backdrop-blur"
        style={{ height: `${topBarHeight}px` }}
      >
        <TopBarMobile 
          user={user}
          theme={theme}
          isTelegramWebApp={isTelegramWebApp}
        />
      </div>

      {/* KPI Stack */}
      <div 
        className={cn(
          "flex-shrink-0 border-b border-border bg-card transition-all duration-300",
          isExpanded ? "shadow-md" : ""
        )}
        style={{ height: `${kpiStackHeight}px` }}
      >
        <KPIStack 
          isExpanded={isExpanded}
          onToggle={() => setIsExpanded(!isExpanded)}
          onHaptic={() => isTelegramWebApp && hapticFeedback('impact', 'light')}
        />
      </div>

      {/* Main Content Area */}
      <div 
        className="flex-1 overflow-hidden"
        style={{ height: `${availableHeight}px` }}
      >
        {/* Chart Tab */}
        {activeTab === 'chart' && (
          <div className="h-full p-2">
            <ChartWatermark className="h-full rounded-lg border border-border bg-card">
              <div className="h-full flex items-center justify-center">
                {children}
              </div>
            </ChartWatermark>
          </div>
        )}

        {/* Analysis Tab */}
        {activeTab === 'analysis' && (
          <div className="h-full overflow-y-auto p-4 space-y-4">
            <div className="card">
              <div className="card-header">
                <h3 className="card-title text-lg">AI Analysis</h3>
                <p className="card-description">Real-time market insights</p>
              </div>
              <div className="card-content">
                <p className="text-sm text-muted-foreground">
                  Analysis content will be displayed here...
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Watchlist Tab */}
        {activeTab === 'watchlist' && (
          <div className="h-full overflow-y-auto p-4 space-y-4">
            <div className="card">
              <div className="card-header">
                <h3 className="card-title text-lg">Watchlist</h3>
                <p className="card-description">Your tracked symbols</p>
              </div>
              <div className="card-content">
                <div className="space-y-3">
                  {['BTCUSDT', 'ETHUSDT', 'ADAUSDT'].map(symbol => (
                    <div key={symbol} className="flex items-center justify-between p-3 bg-accent rounded-lg">
                      <div>
                        <p className="font-medium">{symbol}</p>
                        <p className="text-sm text-muted-foreground">Crypto</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-green-600">+2.45%</p>
                        <p className="text-sm text-muted-foreground">$43,250</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="h-full overflow-y-auto p-4 space-y-4">
            <div className="card">
              <div className="card-header">
                <h3 className="card-title text-lg">Settings</h3>
                <p className="card-description">App preferences</p>
              </div>
              <div className="card-content space-y-4">
                {/* User Info */}
                {user && (
                  <div className="p-3 bg-accent rounded-lg">
                    <p className="font-medium">{user.first_name} {user.last_name}</p>
                    <p className="text-sm text-muted-foreground">@{user.username}</p>
                  </div>
                )}

                {/* Settings Options */}
                <div className="space-y-2">
                  <button className="w-full p-3 text-left bg-accent hover:bg-accent/80 rounded-lg transition-colors">
                    <p className="font-medium">Notifications</p>
                    <p className="text-sm text-muted-foreground">Manage alerts and updates</p>
                  </button>
                  
                  <button className="w-full p-3 text-left bg-accent hover:bg-accent/80 rounded-lg transition-colors">
                    <p className="font-medium">Default Symbol</p>
                    <p className="text-sm text-muted-foreground">Set your preferred trading pair</p>
                  </button>
                  
                  <button className="w-full p-3 text-left bg-accent hover:bg-accent/80 rounded-lg transition-colors">
                    <p className="font-medium">Timeframe</p>
                    <p className="text-sm text-muted-foreground">Default chart timeframe</p>
                  </button>
                </div>

                {/* App Info */}
                <div className="pt-4 border-t border-border">
                  <p className="text-sm text-muted-foreground text-center">
                    ChartGenius v2.0
                  </p>
                  <p className="text-xs text-muted-foreground text-center mt-1">
                    Powered by AI • Real-time data
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Tabs */}
      <div 
        className="flex-shrink-0 border-t border-border bg-card"
        style={{ height: `${bottomTabsHeight}px` }}
      >
        <BottomTabs 
          activeTab={activeTab}
          onTabChange={handleTabChange}
          isTelegramWebApp={isTelegramWebApp}
        />
      </div>

      {/* Telegram WebApp specific styles */}
      {isTelegramWebApp && (
        <style jsx>{`
          .twa-viewport {
            overscroll-behavior: contain;
            -webkit-overflow-scrolling: touch;
            user-select: none;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
          }
        `}</style>
      )}
    </div>
  )
}

export default TelegramLayout
