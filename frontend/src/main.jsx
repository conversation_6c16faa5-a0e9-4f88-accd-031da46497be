import React from 'react'
import { createRoot } from 'react-dom/client'
import { Provider } from 'react-redux'
import { <PERSON>rowserRouter } from 'react-router-dom'
import App from './App'
import { store } from './store'
import './index.css'

const container = document.getElementById('root')
const root = createRoot(container)

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </Provider>
  </React.StrictMode>
)

