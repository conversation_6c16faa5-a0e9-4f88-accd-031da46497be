import { useState, useEffect, useCallback } from 'react'
import { isTelegramWebApp } from '../lib/utils'

/**
 * Custom hook for Telegram WebApp integration
 * Provides access to Telegram WebApp API and user data
 */
export function useTelegramWebApp() {
  const [isReady, setIsReady] = useState(false)
  const [user, setUser] = useState(null)
  const [initData, setInitData] = useState(null)
  const [theme, setTheme] = useState('light')
  const [isExpanded, setIsExpanded] = useState(false)
  const [viewportHeight, setViewportHeight] = useState(window.innerHeight)

  // Initialize Telegram WebApp
  useEffect(() => {
    if (!isTelegramWebApp()) {
      return
    }

    const tg = window.Telegram.WebApp

    // Set up the WebApp
    tg.ready()
    tg.expand()
    
    // Get initial data
    setInitData(tg.initData)
    setUser(tg.initDataUnsafe?.user || null)
    setTheme(tg.colorScheme || 'light')
    setIsExpanded(tg.isExpanded)
    setViewportHeight(tg.viewportHeight)
    
    setIsReady(true)

    // Set up event listeners
    const handleThemeChange = () => {
      setTheme(tg.colorScheme || 'light')
    }

    const handleViewportChange = () => {
      setIsExpanded(tg.isExpanded)
      setViewportHeight(tg.viewportHeight)
    }

    tg.onEvent('themeChanged', handleThemeChange)
    tg.onEvent('viewportChanged', handleViewportChange)

    // Cleanup
    return () => {
      tg.offEvent('themeChanged', handleThemeChange)
      tg.offEvent('viewportChanged', handleViewportChange)
    }
  }, [])

  // Show main button
  const showMainButton = useCallback((text, onClick) => {
    if (!isTelegramWebApp()) return

    const tg = window.Telegram.WebApp
    tg.MainButton.setText(text)
    tg.MainButton.show()
    tg.MainButton.onClick(onClick)
  }, [])

  // Hide main button
  const hideMainButton = useCallback(() => {
    if (!isTelegramWebApp()) return

    const tg = window.Telegram.WebApp
    tg.MainButton.hide()
    tg.MainButton.offClick()
  }, [])

  // Show back button
  const showBackButton = useCallback((onClick) => {
    if (!isTelegramWebApp()) return

    const tg = window.Telegram.WebApp
    tg.BackButton.show()
    tg.BackButton.onClick(onClick)
  }, [])

  // Hide back button
  const hideBackButton = useCallback(() => {
    if (!isTelegramWebApp()) return

    const tg = window.Telegram.WebApp
    tg.BackButton.hide()
    tg.BackButton.offClick()
  }, [])

  // Show popup
  const showPopup = useCallback((params) => {
    if (!isTelegramWebApp()) return Promise.resolve()

    const tg = window.Telegram.WebApp
    return new Promise((resolve) => {
      tg.showPopup(params, resolve)
    })
  }, [])

  // Show alert
  const showAlert = useCallback((message) => {
    if (!isTelegramWebApp()) {
      alert(message)
      return Promise.resolve()
    }

    const tg = window.Telegram.WebApp
    return new Promise((resolve) => {
      tg.showAlert(message, resolve)
    })
  }, [])

  // Show confirm
  const showConfirm = useCallback((message) => {
    if (!isTelegramWebApp()) {
      return Promise.resolve(confirm(message))
    }

    const tg = window.Telegram.WebApp
    return new Promise((resolve) => {
      tg.showConfirm(message, resolve)
    })
  }, [])

  // Close WebApp
  const close = useCallback(() => {
    if (!isTelegramWebApp()) return

    const tg = window.Telegram.WebApp
    tg.close()
  }, [])

  // Send data to bot
  const sendData = useCallback((data) => {
    if (!isTelegramWebApp()) return

    const tg = window.Telegram.WebApp
    tg.sendData(JSON.stringify(data))
  }, [])

  // Open link
  const openLink = useCallback((url, options = {}) => {
    if (!isTelegramWebApp()) {
      window.open(url, '_blank')
      return
    }

    const tg = window.Telegram.WebApp
    tg.openLink(url, options)
  }, [])

  // Open telegram link
  const openTelegramLink = useCallback((url) => {
    if (!isTelegramWebApp()) {
      window.open(url, '_blank')
      return
    }

    const tg = window.Telegram.WebApp
    tg.openTelegramLink(url)
  }, [])

  // Haptic feedback
  const hapticFeedback = useCallback((type = 'impact', style = 'medium') => {
    if (!isTelegramWebApp()) return

    const tg = window.Telegram.WebApp
    if (type === 'impact') {
      tg.HapticFeedback.impactOccurred(style)
    } else if (type === 'notification') {
      tg.HapticFeedback.notificationOccurred(style)
    } else if (type === 'selection') {
      tg.HapticFeedback.selectionChanged()
    }
  }, [])

  // Request write access
  const requestWriteAccess = useCallback(() => {
    if (!isTelegramWebApp()) return Promise.resolve(false)

    const tg = window.Telegram.WebApp
    return new Promise((resolve) => {
      tg.requestWriteAccess(resolve)
    })
  }, [])

  // Request contact
  const requestContact = useCallback(() => {
    if (!isTelegramWebApp()) return Promise.resolve(false)

    const tg = window.Telegram.WebApp
    return new Promise((resolve) => {
      tg.requestContact(resolve)
    })
  }, [])

  return {
    // State
    isReady,
    isTelegramWebApp: isTelegramWebApp(),
    user,
    initData,
    theme,
    isExpanded,
    viewportHeight,

    // Methods
    showMainButton,
    hideMainButton,
    showBackButton,
    hideBackButton,
    showPopup,
    showAlert,
    showConfirm,
    close,
    sendData,
    openLink,
    openTelegramLink,
    hapticFeedback,
    requestWriteAccess,
    requestContact,

    // Telegram WebApp object (for advanced usage)
    webApp: isTelegramWebApp() ? window.Telegram.WebApp : null
  }
}

export default useTelegramWebApp
