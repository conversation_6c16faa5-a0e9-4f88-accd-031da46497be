import React from 'react'
import { 
  Bar<PERSON>hart3, 
  <PERSON>, 
  <PERSON>, 
  Settings,
  TrendingUp
} from 'lucide-react'
import { cn } from '../../lib/utils'

/**
 * Bottom Tabs component for Telegram WebApp
 * Navigation tabs optimized for mobile touch interaction
 */
export const BottomTabs = ({ 
  activeTab, 
  onTabChange, 
  isTelegramWebApp = false 
}) => {
  const tabs = [
    {
      id: 'chart',
      label: 'Chart',
      icon: BarChart3,
      color: 'text-blue-600 dark:text-blue-400'
    },
    {
      id: 'analysis',
      label: 'Analysis',
      icon: Brain,
      color: 'text-purple-600 dark:text-purple-400'
    },
    {
      id: 'watchlist',
      label: 'Watchlist',
      icon: Eye,
      color: 'text-green-600 dark:text-green-400'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      color: 'text-gray-600 dark:text-gray-400'
    }
  ]

  const handleTabClick = (tabId) => {
    onTabChange(tabId)
  }

  return (
    <div className="h-full flex items-center justify-around px-2">
      {tabs.map((tab) => {
        const Icon = tab.icon
        const isActive = activeTab === tab.id
        
        return (
          <button
            key={tab.id}
            onClick={() => handleTabClick(tab.id)}
            className={cn(
              "flex flex-col items-center justify-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200",
              "min-w-0 flex-1 max-w-20",
              isActive 
                ? "bg-primary/10 text-primary" 
                : "text-muted-foreground hover:text-foreground hover:bg-accent/50",
              // Enhanced touch targets for mobile
              isTelegramWebApp && "active:scale-95 active:bg-accent"
            )}
          >
            <div className={cn(
              "relative",
              isActive && "animate-pulse"
            )}>
              <Icon 
                size={20} 
                className={cn(
                  "transition-colors",
                  isActive ? "text-primary" : ""
                )} 
              />
              
              {/* Active indicator dot */}
              {isActive && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full animate-pulse" />
              )}
            </div>
            
            <span className={cn(
              "text-xs font-medium truncate transition-colors",
              isActive ? "text-primary" : ""
            )}>
              {tab.label}
            </span>
          </button>
        )
      })}
    </div>
  )
}

export default BottomTabs
