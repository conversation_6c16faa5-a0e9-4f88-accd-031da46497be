import React from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  ChevronUp, 
  ChevronDown,
  DollarSign,
  BarChart3,
  Volume2,
  Clock
} from 'lucide-react'
import { cn, formatCurrency, formatPercentage, formatNumber } from '../../lib/utils'

/**
 * KPI Stack component for Telegram WebApp
 * Shows key metrics in a compact, expandable format
 */
export const KPIStack = ({ 
  isExpanded, 
  onToggle, 
  onHaptic,
  data = null 
}) => {
  // Mock data - replace with real data from props/context
  const kpiData = data || {
    symbol: 'BTCUSDT',
    price: 43250.50,
    change24h: 2.45,
    changeAmount: 1032.75,
    volume24h: 2400000000,
    high24h: 44100.00,
    low24h: 42800.00,
    marketCap: 850000000000,
    rsi: 65.2,
    lastUpdated: new Date().toLocaleTimeString()
  }

  const isPositive = kpiData.change24h > 0

  const handleToggle = () => {
    if (onHaptic) onHaptic()
    onToggle()
  }

  return (
    <div className="h-full flex flex-col">
      {/* Main KPI Row - Always Visible */}
      <div className="flex-1 px-4 py-2 flex items-center justify-between">
        {/* Symbol & Price */}
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <h2 className="text-lg font-bold text-foreground">{kpiData.symbol}</h2>
            <div className={cn(
              "flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium",
              isPositive 
                ? "bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300"
                : "bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300"
            )}>
              {isPositive ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
              <span>{formatPercentage(Math.abs(kpiData.change24h))}</span>
            </div>
          </div>
          <div className="flex items-baseline space-x-2">
            <span className="text-xl font-bold text-foreground">
              {formatCurrency(kpiData.price)}
            </span>
            <span className={cn(
              "text-sm font-medium",
              isPositive ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
            )}>
              {isPositive ? '+' : ''}{formatCurrency(kpiData.changeAmount)}
            </span>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <p className="text-xs text-muted-foreground">Volume</p>
            <p className="text-sm font-medium">{formatNumber(kpiData.volume24h)}</p>
          </div>
          
          <div className="text-right">
            <p className="text-xs text-muted-foreground">RSI</p>
            <p className={cn(
              "text-sm font-medium",
              kpiData.rsi > 70 ? "text-red-600" : 
              kpiData.rsi < 30 ? "text-green-600" : "text-foreground"
            )}>
              {kpiData.rsi}
            </p>
          </div>

          {/* Expand/Collapse Button */}
          <button
            onClick={handleToggle}
            className="p-2 hover:bg-accent rounded-md transition-colors"
          >
            {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </button>
        </div>
      </div>

      {/* Expanded KPI Details */}
      {isExpanded && (
        <div className="px-4 py-2 border-t border-border bg-muted/30">
          <div className="grid grid-cols-4 gap-4">
            {/* High 24h */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <TrendingUp size={12} className="text-green-600 mr-1" />
                <span className="text-xs text-muted-foreground">High</span>
              </div>
              <p className="text-sm font-medium text-foreground">
                {formatCurrency(kpiData.high24h)}
              </p>
            </div>

            {/* Low 24h */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <TrendingDown size={12} className="text-red-600 mr-1" />
                <span className="text-xs text-muted-foreground">Low</span>
              </div>
              <p className="text-sm font-medium text-foreground">
                {formatCurrency(kpiData.low24h)}
              </p>
            </div>

            {/* Market Cap */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <DollarSign size={12} className="text-blue-600 mr-1" />
                <span className="text-xs text-muted-foreground">MCap</span>
              </div>
              <p className="text-sm font-medium text-foreground">
                {formatNumber(kpiData.marketCap)}
              </p>
            </div>

            {/* Last Updated */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <Clock size={12} className="text-gray-600 mr-1" />
                <span className="text-xs text-muted-foreground">Updated</span>
              </div>
              <p className="text-sm font-medium text-foreground">
                {kpiData.lastUpdated}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default KPIStack
