import React, { useState } from 'react'
import * as Tabs from '@radix-ui/react-tabs'
import { 
  FileText, 
  MessageSquare, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Copy,
  Download,
  RefreshCw
} from 'lucide-react'
import { cn } from '../lib/utils'
import { SummaryWatermark, ExplanationsWatermark } from './ui/Watermark'

/**
 * Right Pane component for Summary and Explanations
 * 28% width when expanded, contains tabs for different content
 */
export const RightPane = ({ collapsed, onToggle }) => {
  const [activeTab, setActiveTab] = useState('summary')
  const [isLoading, setIsLoading] = useState(false)

  // Mock data - replace with real data from API
  const summaryData = {
    symbol: 'BTCUSDT',
    price: 43250.50,
    change24h: 2.45,
    trend: 'bullish',
    support: 42800,
    resistance: 44000,
    rsi: 65.2,
    volume: '2.4B',
    lastUpdated: new Date().toLocaleTimeString()
  }

  const explanationData = {
    technicalAnalysis: `Based on the current chart pattern, Bitcoin is showing strong bullish momentum with price breaking above the 20-day moving average. The RSI at 65.2 indicates healthy buying pressure without being overbought.`,
    keyLevels: `Support is established at $42,800 with strong buying interest. Resistance at $44,000 represents a key psychological level that needs to be broken for further upside.`,
    recommendation: `Consider entering long positions on pullbacks to support levels. Set stop-loss below $42,500 and target $44,500 for the next resistance level.`,
    riskFactors: `Monitor overall market sentiment and Bitcoin dominance. High volatility expected around major news events.`
  }

  if (collapsed) {
    return (
      <div className="h-full w-16 flex flex-col items-center py-4 space-y-4">
        <button
          onClick={onToggle}
          className="p-2 hover:bg-accent rounded-md transition-colors"
          title="Expand right pane"
        >
          <FileText size={20} />
        </button>
        
        {/* Collapsed icons */}
        <div className="flex flex-col space-y-2">
          <div className="p-2 rounded-md bg-primary/10" title="Summary">
            <TrendingUp size={16} className="text-primary" />
          </div>
          <div className="p-2 rounded-md" title="Explanations">
            <MessageSquare size={16} className="text-muted-foreground" />
          </div>
        </div>
      </div>
    )
  }

  const handleRefresh = async () => {
    setIsLoading(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsLoading(false)
  }

  const handleCopy = (content) => {
    navigator.clipboard.writeText(content)
    // Show toast notification
  }

  const handleExport = () => {
    // Export functionality
    console.log('Exporting analysis...')
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-foreground">Analysis</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-1.5 hover:bg-accent rounded-md transition-colors disabled:opacity-50"
              title="Refresh analysis"
            >
              <RefreshCw size={16} className={cn(isLoading && "animate-spin")} />
            </button>
            <button
              onClick={handleExport}
              className="p-1.5 hover:bg-accent rounded-md transition-colors"
              title="Export analysis"
            >
              <Download size={16} />
            </button>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">Real-time market insights</p>
      </div>

      {/* Tabs */}
      <Tabs.Root value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <Tabs.List className="flex border-b border-border">
          <Tabs.Trigger 
            value="summary"
            className="flex-1 px-4 py-3 text-sm font-medium text-muted-foreground hover:text-foreground data-[state=active]:text-foreground data-[state=active]:border-b-2 data-[state=active]:border-primary transition-colors"
          >
            <div className="flex items-center space-x-2">
              <TrendingUp size={16} />
              <span>Summary</span>
            </div>
          </Tabs.Trigger>
          <Tabs.Trigger 
            value="explanations"
            className="flex-1 px-4 py-3 text-sm font-medium text-muted-foreground hover:text-foreground data-[state=active]:text-foreground data-[state=active]:border-b-2 data-[state=active]:border-primary transition-colors"
          >
            <div className="flex items-center space-x-2">
              <MessageSquare size={16} />
              <span>Details</span>
            </div>
          </Tabs.Trigger>
        </Tabs.List>

        {/* Summary Tab */}
        <Tabs.Content value="summary" className="flex-1 overflow-y-auto">
          <SummaryWatermark className="h-full">
            <div className="p-4 space-y-4">
              {/* Price Overview */}
              <div className="card">
                <div className="card-header pb-3">
                  <div className="flex items-center justify-between">
                    <h3 className="card-title text-base">{summaryData.symbol}</h3>
                    <button
                      onClick={() => handleCopy(`${summaryData.symbol}: $${summaryData.price}`)}
                      className="p-1 hover:bg-accent rounded-md transition-colors"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                </div>
                <div className="card-content">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold">${summaryData.price.toLocaleString()}</span>
                      <div className={cn(
                        "flex items-center space-x-1",
                        summaryData.change24h > 0 ? "text-green-600" : "text-red-600"
                      )}>
                        {summaryData.change24h > 0 ? <TrendingUp size={16} /> : <TrendingDown size={16} />}
                        <span className="font-medium">{summaryData.change24h}%</span>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">Last updated: {summaryData.lastUpdated}</p>
                  </div>
                </div>
              </div>

              {/* Key Metrics */}
              <div className="card">
                <div className="card-header pb-3">
                  <h3 className="card-title text-sm">Key Metrics</h3>
                </div>
                <div className="card-content">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Support</span>
                      <span className="text-sm font-medium">${summaryData.support.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Resistance</span>
                      <span className="text-sm font-medium">${summaryData.resistance.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">RSI</span>
                      <span className="text-sm font-medium">{summaryData.rsi}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Volume 24h</span>
                      <span className="text-sm font-medium">{summaryData.volume}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Trend Analysis */}
              <div className="card">
                <div className="card-header pb-3">
                  <h3 className="card-title text-sm">Trend Analysis</h3>
                </div>
                <div className="card-content">
                  <div className="flex items-center space-x-2">
                    <div className={cn(
                      "w-3 h-3 rounded-full",
                      summaryData.trend === 'bullish' ? "bg-green-500" :
                      summaryData.trend === 'bearish' ? "bg-red-500" : "bg-gray-500"
                    )} />
                    <span className="text-sm font-medium capitalize">{summaryData.trend}</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    {summaryData.trend === 'bullish' 
                      ? "Strong upward momentum with increasing volume"
                      : summaryData.trend === 'bearish'
                      ? "Downward pressure with selling volume"
                      : "Sideways movement, waiting for direction"
                    }
                  </p>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-2">
                <button className="w-full btn btn-primary btn-sm">
                  Get Full Analysis
                </button>
                <button className="w-full btn btn-outline btn-sm">
                  Add to Watchlist
                </button>
              </div>
            </div>
          </SummaryWatermark>
        </Tabs.Content>

        {/* Explanations Tab */}
        <Tabs.Content value="explanations" className="flex-1 overflow-y-auto">
          <ExplanationsWatermark className="h-full">
            <div className="p-4 space-y-4">
              {/* Technical Analysis */}
              <div className="card">
                <div className="card-header pb-3">
                  <div className="flex items-center justify-between">
                    <h3 className="card-title text-sm">Technical Analysis</h3>
                    <button
                      onClick={() => handleCopy(explanationData.technicalAnalysis)}
                      className="p-1 hover:bg-accent rounded-md transition-colors"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                </div>
                <div className="card-content">
                  <p className="text-sm text-foreground leading-relaxed">
                    {explanationData.technicalAnalysis}
                  </p>
                </div>
              </div>

              {/* Key Levels */}
              <div className="card">
                <div className="card-header pb-3">
                  <div className="flex items-center justify-between">
                    <h3 className="card-title text-sm">Key Levels</h3>
                    <button
                      onClick={() => handleCopy(explanationData.keyLevels)}
                      className="p-1 hover:bg-accent rounded-md transition-colors"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                </div>
                <div className="card-content">
                  <p className="text-sm text-foreground leading-relaxed">
                    {explanationData.keyLevels}
                  </p>
                </div>
              </div>

              {/* Recommendation */}
              <div className="card">
                <div className="card-header pb-3">
                  <div className="flex items-center justify-between">
                    <h3 className="card-title text-sm">Trading Recommendation</h3>
                    <button
                      onClick={() => handleCopy(explanationData.recommendation)}
                      className="p-1 hover:bg-accent rounded-md transition-colors"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                </div>
                <div className="card-content">
                  <p className="text-sm text-foreground leading-relaxed">
                    {explanationData.recommendation}
                  </p>
                </div>
              </div>

              {/* Risk Factors */}
              <div className="card">
                <div className="card-header pb-3">
                  <div className="flex items-center justify-between">
                    <h3 className="card-title text-sm">Risk Factors</h3>
                    <button
                      onClick={() => handleCopy(explanationData.riskFactors)}
                      className="p-1 hover:bg-accent rounded-md transition-colors"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                </div>
                <div className="card-content">
                  <p className="text-sm text-foreground leading-relaxed">
                    {explanationData.riskFactors}
                  </p>
                </div>
              </div>

              {/* Disclaimer */}
              <div className="p-3 bg-muted/50 rounded-md">
                <p className="text-xs text-muted-foreground">
                  <strong>Disclaimer:</strong> This analysis is for educational purposes only and should not be considered as financial advice. Always do your own research before making investment decisions.
                </p>
              </div>
            </div>
          </ExplanationsWatermark>
        </Tabs.Content>
      </Tabs.Root>
    </div>
  )
}

export default RightPane
