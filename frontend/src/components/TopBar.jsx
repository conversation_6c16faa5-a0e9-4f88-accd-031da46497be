import React, { useState } from 'react'
import { 
  Menu, 
  X, 
  Settings, 
  User, 
  Moon, 
  Sun, 
  ChevronDown,
  Activity,
  Zap,
  BarChart3
} from 'lucide-react'
import { cn } from '../lib/utils'
import { useDarkMode } from '../hooks/useDarkMode'
import { isTelegramWebApp } from '../lib/utils'

/**
 * Top Bar component for ChartGenius v2.0
 * Contains logo, navigation, user menu, and controls
 */
export const TopBar = ({ 
  onSidebarToggle, 
  onRightPaneToggle, 
  sidebarCollapsed, 
  rightPaneCollapsed 
}) => {
  const { isDark, toggle: toggleDarkMode, canToggle } = useDarkMode()
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [settingsMenuOpen, setSettingsMenuOpen] = useState(false)

  // Mock user data - replace with real user context
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    tier: "Pro",
    avatar: null
  }

  return (
    <header className="h-16 border-b border-border bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
      <div className="h-full px-4 flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Sidebar Toggle */}
          <button
            onClick={onSidebarToggle}
            className="p-2 hover:bg-accent rounded-md transition-colors"
            title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {sidebarCollapsed ? <Menu size={20} /> : <X size={20} />}
          </button>

          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <BarChart3 size={20} className="text-primary-foreground" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-lg font-bold text-foreground">ChartGenius</h1>
              <p className="text-xs text-muted-foreground">v2.0</p>
            </div>
          </div>

          {/* Status Indicators */}
          <div className="hidden md:flex items-center space-x-2">
            {/* LLM Status */}
            <div className="flex items-center space-x-1 px-2 py-1 bg-green-100 dark:bg-green-900/20 rounded-md">
              <Zap size={12} className="text-green-600 dark:text-green-400" />
              <span className="text-xs text-green-700 dark:text-green-300">AI Ready</span>
            </div>

            {/* Connection Status */}
            <div className="flex items-center space-x-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/20 rounded-md">
              <Activity size={12} className="text-blue-600 dark:text-blue-400" />
              <span className="text-xs text-blue-700 dark:text-blue-300">Live</span>
            </div>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {/* Dark Mode Toggle (only if not in Telegram) */}
          {canToggle && (
            <button
              onClick={toggleDarkMode}
              className="p-2 hover:bg-accent rounded-md transition-colors"
              title={isDark ? "Switch to light mode" : "Switch to dark mode"}
            >
              {isDark ? <Sun size={20} /> : <Moon size={20} />}
            </button>
          )}

          {/* Settings Menu */}
          <div className="relative">
            <button
              onClick={() => setSettingsMenuOpen(!settingsMenuOpen)}
              className="p-2 hover:bg-accent rounded-md transition-colors"
              title="Settings"
            >
              <Settings size={20} />
            </button>

            {settingsMenuOpen && (
              <div className="absolute right-0 top-full mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-50">
                <div className="py-1">
                  <button className="w-full px-4 py-2 text-left text-sm hover:bg-accent transition-colors">
                    LLM Settings
                  </button>
                  <button className="w-full px-4 py-2 text-left text-sm hover:bg-accent transition-colors">
                    Chart Settings
                  </button>
                  <button className="w-full px-4 py-2 text-left text-sm hover:bg-accent transition-colors">
                    Notifications
                  </button>
                  <hr className="my-1 border-border" />
                  <button className="w-full px-4 py-2 text-left text-sm hover:bg-accent transition-colors">
                    Preferences
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* User Menu */}
          <div className="relative">
            <button
              onClick={() => setUserMenuOpen(!userMenuOpen)}
              className="flex items-center space-x-2 p-2 hover:bg-accent rounded-md transition-colors"
            >
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                {user.avatar ? (
                  <img src={user.avatar} alt={user.name} className="w-8 h-8 rounded-full" />
                ) : (
                  <User size={16} className="text-primary-foreground" />
                )}
              </div>
              <div className="hidden sm:block text-left">
                <p className="text-sm font-medium text-foreground">{user.name}</p>
                <p className="text-xs text-muted-foreground">{user.tier}</p>
              </div>
              <ChevronDown size={16} className="text-muted-foreground" />
            </button>

            {userMenuOpen && (
              <div className="absolute right-0 top-full mt-2 w-56 bg-popover border border-border rounded-md shadow-lg z-50">
                <div className="py-1">
                  <div className="px-4 py-2 border-b border-border">
                    <p className="text-sm font-medium text-foreground">{user.name}</p>
                    <p className="text-xs text-muted-foreground">{user.email}</p>
                    <div className="mt-1">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                        {user.tier} Plan
                      </span>
                    </div>
                  </div>
                  <button className="w-full px-4 py-2 text-left text-sm hover:bg-accent transition-colors">
                    Profile
                  </button>
                  <button className="w-full px-4 py-2 text-left text-sm hover:bg-accent transition-colors">
                    Billing
                  </button>
                  <button className="w-full px-4 py-2 text-left text-sm hover:bg-accent transition-colors">
                    API Keys
                  </button>
                  <hr className="my-1 border-border" />
                  <button className="w-full px-4 py-2 text-left text-sm text-destructive hover:bg-accent transition-colors">
                    Sign Out
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Right Pane Toggle */}
          <button
            onClick={onRightPaneToggle}
            className="p-2 hover:bg-accent rounded-md transition-colors"
            title={rightPaneCollapsed ? "Expand right pane" : "Collapse right pane"}
          >
            {rightPaneCollapsed ? <Menu size={20} /> : <X size={20} />}
          </button>
        </div>
      </div>

      {/* Click outside to close menus */}
      {(userMenuOpen || settingsMenuOpen) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => {
            setUserMenuOpen(false)
            setSettingsMenuOpen(false)
          }}
        />
      )}
    </header>
  )
}

export default TopBar
