# ADD‑ON к ТЗ v 2.1 — Переход на Wallet Pay (TON)

*(дополнительный документ; встраивается в раздел «Монетизация» ТЗ v 2.1 и заменяет Stripe/2Checkout)*

---

## 1. Изменения

| Было                           | Становится                       |
|-------------------------------|----------------------------------|
| Stripe Checkout (Desktop/TWA) | **Wallet Pay** (TON) для всех каналов |
| Telegram Payments (Stripe)    | `payments.createInvoice` (Wallet Pay) |

---

## 2. Поток оплаты

```mermaid
sequenceDiagram
  participant U as User
  participant B as Bot / Web
  participant WP as Wallet Pay
  participant API as Backend
  U->>B: Нажим<PERSON>ет «Подписка»
  B->>WP: POST /invoice (plan, amount)
  WP->>B: invoice_link
  U->>WP: Оплата TON/USDT
  WP-->>B: webhook invoiceIsPaid
  B->>API: /api/pay/walletpay
  API->>AJD: tier = premium
  B->>U: ✅ Подписка активна
```

---

## 3. Изменения в коде

### Telegram‑бот

```python
invoice = await walletpay.create_invoice(
    asset="TON", amount=5,
    description="ChartGenius Premium 30 days"
)
await update.effective_chat.send_invoice_link(invoice["url"])
```

* Middleware обрабатывает webhook `invoiceIsPaid`.

### Backend

* `POST /api/pay/walletpay` — валидация подписи (`X-WalletPay-Signature`) и обновление `subscriptions`.

### Frontend

```ts
const {{invoiceUrl}} = await api.createInvoice(plan);
window.open(invoiceUrl, "_blank");
```

Polling `/api/subscription` обновляет бейдж.

---

## 4. Env‑переменные

```dotenv
WALLETPAY_TOKEN=prod_xxx
WALLETPAY_WEBHOOK_SECRET=whsec_...
TON_ASSET=TON
PLAN_PREMIUM_PRICE=5   # 5 TON
```

---

## 5. Тест‑кейсы

| Уровень     | Проверка                                   |
|-------------|--------------------------------------------|
| Unit        | mock invoice.create / invoice.paid payload |
| Integration | PTB: `/subscribe` → URL, webhook → tier    |
| E2E         | Playwright flow «Оплатить TON»             |

---

## 6. Спринты

* **S3** → задача *Payments* = внедрение Wallet Pay (≈ 5 дней dev + 2 дня QA).

---

## 7. Удалить

* Пакеты `stripe`, `@stripe/stripe-js`, переменные `STRIPE_*`, docs Stripe.
