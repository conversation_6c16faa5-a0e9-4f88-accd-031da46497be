# bot/handlers/payments.py

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, LabeledPrice
from telegram.ext import ContextTypes, ConversationHandler

from backend.services.oracle_client import oracle_client
from backend.config.config import logger
from bot.services.wallet_pay_client import get_wallet_pay_client

# Conversation states
SELECTING_PLAN, CONFIRMING_PAYMENT, PROCESSING_PAYMENT = range(3)

class PaymentHandler:
    """
    Обработчик платежей для Telegram бота v2
    Поддерживает Wallet Pay и Telegram Payments
    """

    def __init__(self, telegram_payment_token: str = None):
        self.telegram_payment_token = telegram_payment_token
        self.wallet_pay_client = get_wallet_pay_client()
        
        # Планы подписки (цены в TON)
        self.subscription_plans = {
            'basic': {
                'name': 'Basic Plan',
                'price': 5.0,  # 5 TON
                'currency': 'TON',
                'duration_days': 30,
                'features': [
                    '✅ AI Analysis (100 requests/month)',
                    '✅ Basic Technical Indicators',
                    '✅ 5 Watchlist symbols',
                    '✅ Email support'
                ]
            },
            'pro': {
                'name': 'Pro Plan',
                'price': 10.0,  # 10 TON
                'currency': 'TON',
                'duration_days': 30,
                'features': [
                    '✅ AI Analysis (Unlimited)',
                    '✅ Advanced Technical Indicators',
                    '✅ Unlimited Watchlist',
                    '✅ Real-time alerts',
                    '✅ Priority support',
                    '✅ Export features'
                ]
            },
            'premium': {
                'name': 'Premium Plan',
                'price': 25.0,  # 25 TON
                'currency': 'TON',
                'duration_days': 30,
                'features': [
                    '✅ Everything in Pro',
                    '✅ Custom AI models',
                    '✅ API access',
                    '✅ White-label features',
                    '✅ 24/7 support',
                    '✅ Custom integrations'
                ]
            }
        }
    
    async def show_subscription_plans(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Показать доступные планы подписки"""
        try:
            user_id = str(update.effective_user.id)
            
            # Получаем текущую подписку пользователя
            user_data = oracle_client.get_document('users', user_id)
            current_plan = user_data.get('tier', 'free') if user_data else 'free'
            
            message = "💎 <b>ChartGenius Subscription Plans</b>\n\n"
            message += f"Current plan: <b>{current_plan.title()}</b>\n\n"
            
            keyboard = []
            
            for plan_id, plan_info in self.subscription_plans.items():
                price_str = f"${plan_info['price'] / 100:.2f}"
                
                plan_text = f"<b>{plan_info['name']}</b> - {price_str}/month\n"
                for feature in plan_info['features']:
                    plan_text += f"{feature}\n"
                plan_text += "\n"
                
                message += plan_text
                
                # Добавляем кнопку для выбора плана
                if current_plan != plan_id:
                    keyboard.append([
                        InlineKeyboardButton(
                            f"Subscribe to {plan_info['name']} ({price_str})",
                            callback_data=f"subscribe_{plan_id}"
                        )
                    ])
            
            keyboard.append([InlineKeyboardButton("❌ Cancel", callback_data="cancel_subscription")])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                message,
                parse_mode='HTML',
                reply_markup=reply_markup
            )
            
            return SELECTING_PLAN
            
        except Exception as e:
            logger.error(f"Ошибка показа планов подписки: {e}")
            await update.message.reply_text(
                "❌ Произошла ошибка при загрузке планов подписки. Попробуйте позже."
            )
            return ConversationHandler.END
    
    async def handle_plan_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Обработка выбора плана"""
        try:
            query = update.callback_query
            await query.answer()
            
            if query.data == "cancel_subscription":
                await query.edit_message_text("❌ Подписка отменена.")
                return ConversationHandler.END
            
            if not query.data.startswith("subscribe_"):
                await query.edit_message_text("❌ Неверный выбор плана.")
                return ConversationHandler.END
            
            plan_id = query.data.replace("subscribe_", "")
            
            if plan_id not in self.subscription_plans:
                await query.edit_message_text("❌ План не найден.")
                return ConversationHandler.END
            
            plan_info = self.subscription_plans[plan_id]
            context.user_data['selected_plan'] = plan_id
            
            # Показываем варианты оплаты
            keyboard = [
                [InlineKeyboardButton("💎 Wallet Pay (TON)", callback_data="payment_wallet_pay")],
                [InlineKeyboardButton("⭐ Telegram Stars", callback_data="payment_telegram")],
                [InlineKeyboardButton("❌ Cancel", callback_data="cancel_payment")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            message = f"💎 <b>{plan_info['name']}</b>\n\n"
            message += f"💰 Price: {plan_info['price']} {plan_info['currency']}/month\n"
            message += f"⏰ Duration: {plan_info['duration_days']} days\n\n"
            message += "Choose payment method:"
            
            await query.edit_message_text(
                message,
                parse_mode='HTML',
                reply_markup=reply_markup
            )
            
            return CONFIRMING_PAYMENT
            
        except Exception as e:
            logger.error(f"Ошибка выбора плана: {e}")
            await query.edit_message_text("❌ Произошла ошибка. Попробуйте позже.")
            return ConversationHandler.END
    
    async def handle_payment_method(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Обработка выбора метода оплаты"""
        try:
            query = update.callback_query
            await query.answer()
            
            if query.data == "cancel_payment":
                await query.edit_message_text("❌ Оплата отменена.")
                return ConversationHandler.END
            
            plan_id = context.user_data.get('selected_plan')
            if not plan_id:
                await query.edit_message_text("❌ План не выбран.")
                return ConversationHandler.END
            
            plan_info = self.subscription_plans[plan_id]

            if query.data == "payment_wallet_pay":
                return await self._process_wallet_pay_payment(query, context, plan_id, plan_info)
            elif query.data == "payment_telegram":
                return await self._process_telegram_payment(query, context, plan_id, plan_info)
            else:
                await query.edit_message_text("❌ Неверный метод оплаты.")
                return ConversationHandler.END
                
        except Exception as e:
            logger.error(f"Ошибка обработки метода оплаты: {e}")
            await query.edit_message_text("❌ Произошла ошибка. Попробуйте позже.")
            return ConversationHandler.END
    
    async def _process_wallet_pay_payment(self, query, context, plan_id: str, plan_info: Dict) -> int:
        """Обработка оплаты через Wallet Pay"""
        try:
            user_id = str(query.from_user.id)

            if not self.wallet_pay_client.is_configured():
                await query.edit_message_text("❌ Wallet Pay не настроен.")
                return ConversationHandler.END

            # Создаем заказ в Wallet Pay
            external_id = f"subscription_{plan_id}_{user_id}_{int(datetime.utcnow().timestamp())}"

            order_result = await self.wallet_pay_client.create_order(
                amount=plan_info['price'],
                currency=plan_info['currency'],
                description=f"ChartGenius {plan_info['name']} - 1 month subscription",
                external_id=external_id,
                timeout_seconds=3600,  # 1 час на оплату
                customer_telegram_user_id=int(user_id)
            )

            if not order_result.get('success'):
                error_msg = order_result.get('error', 'Unknown error')
                await query.edit_message_text(f"❌ Ошибка создания заказа: {error_msg}")
                return ConversationHandler.END

            # Сохраняем информацию о платеже
            payment_data = {
                'user_id': user_id,
                'plan_id': plan_id,
                'wallet_pay_order_id': order_result['order_id'],
                'external_id': external_id,
                'amount': plan_info['price'],
                'currency': plan_info['currency'],
                'status': 'pending',
                'payment_method': 'wallet_pay',
                'created_at': datetime.utcnow().isoformat()
            }

            oracle_client.insert_document('payments', external_id, payment_data)

            keyboard = [
                [InlineKeyboardButton("💎 Pay with Wallet Pay", url=order_result['pay_link'])],
                [InlineKeyboardButton("📱 Direct Pay", url=order_result['direct_pay_link'])],
                [InlineKeyboardButton("❌ Cancel", callback_data="cancel_payment")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            message = f"💎 <b>Wallet Pay Payment</b>\n\n"
            message += f"Plan: {plan_info['name']}\n"
            message += f"Amount: {plan_info['price']} {plan_info['currency']}\n\n"
            message += "Choose payment option:\n"
            message += "• 💎 Wallet Pay - Open in browser\n"
            message += "• 📱 Direct Pay - Open in Telegram\n\n"
            message += "⏰ Payment expires in 1 hour"

            await query.edit_message_text(
                message,
                parse_mode='HTML',
                reply_markup=reply_markup
            )

            return PROCESSING_PAYMENT

        except Exception as e:
            logger.error(f"Ошибка создания Wallet Pay платежа: {e}")
            await query.edit_message_text("❌ Ошибка создания платежа. Попробуйте позже.")
            return ConversationHandler.END
    
    async def _process_telegram_payment(self, query, context, plan_id: str, plan_info: Dict) -> int:
        """Обработка оплаты через Telegram Stars"""
        try:
            if not self.telegram_payment_token:
                await query.edit_message_text("❌ Telegram Payments недоступны.")
                return ConversationHandler.END
            
            # Конвертируем цену в Telegram Stars (примерно 1 TON = 200 Stars)
            stars_amount = int(plan_info['price'] * 200)  # TON в Stars
            
            # Создаем инвойс для Telegram Stars
            title = f"ChartGenius {plan_info['name']}"
            description = f"1 month subscription to {plan_info['name']}"
            payload = f"subscription_{plan_id}_{query.from_user.id}"
            
            prices = [LabeledPrice(label=title, amount=stars_amount)]
            
            await context.bot.send_invoice(
                chat_id=query.message.chat_id,
                title=title,
                description=description,
                payload=payload,
                provider_token=self.telegram_payment_token,
                currency='XTR',  # Telegram Stars
                prices=prices,
                start_parameter=f"subscription_{plan_id}",
                photo_url="https://your-domain.com/subscription-image.jpg"  # Опционально
            )
            
            await query.edit_message_text(
                f"⭐ Invoice sent for {plan_info['name']}!\n"
                f"Amount: {stars_amount} Telegram Stars"
            )
            
            return PROCESSING_PAYMENT
            
        except Exception as e:
            logger.error(f"Ошибка создания Telegram платежа: {e}")
            await query.edit_message_text("❌ Ошибка создания платежа. Попробуйте позже.")
            return ConversationHandler.END
    
    async def handle_successful_payment(self, user_id: str, plan_id: str, payment_method: str) -> bool:
        """Обработка успешного платежа"""
        try:
            plan_info = self.subscription_plans.get(plan_id)
            if not plan_info:
                logger.error(f"План {plan_id} не найден")
                return False
            
            # Обновляем данные пользователя
            user_data = oracle_client.get_document('users', user_id) or {}
            
            # Вычисляем дату окончания подписки
            expires_at = datetime.utcnow() + timedelta(days=plan_info['duration_days'])
            
            user_data.update({
                'tier': plan_id,
                'expires': expires_at.isoformat(),
                'payment_method': payment_method,
                'subscription_updated_at': datetime.utcnow().isoformat()
            })
            
            # Сохраняем обновленные данные
            success = oracle_client.update_document('users', user_id, user_data)
            if not success:
                success = oracle_client.insert_document('users', user_id, user_data)
            
            if success:
                logger.info(f"Подписка {plan_id} активирована для пользователя {user_id}")
                return True
            else:
                logger.error(f"Ошибка сохранения подписки для пользователя {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"Ошибка обработки успешного платежа: {e}")
            return False
    
    async def cancel_conversation(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Отмена разговора о подписке"""
        await update.message.reply_text("❌ Операция отменена.")
        return ConversationHandler.END

# Глобальный экземпляр обработчика платежей
payment_handler = None

def get_payment_handler(telegram_payment_token: str = None) -> PaymentHandler:
    """Получить глобальный экземпляр обработчика платежей"""
    global payment_handler
    if payment_handler is None:
        payment_handler = PaymentHandler(telegram_payment_token)
    return payment_handler
