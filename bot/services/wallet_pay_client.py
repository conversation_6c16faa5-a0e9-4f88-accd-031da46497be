# bot/services/wallet_pay_client.py

import os
import logging
import hashlib
import hmac
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
import aiohttp
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class WalletPayClient:
    """
    Клиент для работы с Wallet Pay API
    Интеграция с TON-based платежной системой
    """
    
    def __init__(self):
        self.api_key = os.getenv("WALLET_PAY_API_KEY")
        self.secret_key = os.getenv("WALLET_PAY_SECRET_KEY")
        self.base_url = os.getenv("WALLET_PAY_BASE_URL", "https://pay.wallet.tg/wpay/store-api/v1")
        self.webhook_url = os.getenv("WALLET_PAY_WEBHOOK_URL")
        
        if not self.api_key or not self.secret_key:
            logger.warning("Wallet Pay API ключи не установлены")
        
        logger.info("Wallet Pay клиент инициализирован")
    
    def _generate_signature(self, method: str, url_path: str, timestamp: str, body: str = "") -> str:
        """
        Генерация подписи для Wallet Pay API
        
        Args:
            method: HTTP метод
            url_path: Путь URL
            timestamp: Временная метка
            body: Тело запроса
            
        Returns:
            Подпись HMAC-SHA256
        """
        message = f"{method}.{url_path}.{timestamp}.{body}"
        signature = hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def _get_headers(self, method: str, url_path: str, body: str = "") -> Dict[str, str]:
        """
        Получение заголовков для запроса
        
        Args:
            method: HTTP метод
            url_path: Путь URL
            body: Тело запроса
            
        Returns:
            Заголовки запроса
        """
        timestamp = str(int(datetime.utcnow().timestamp()))
        signature = self._generate_signature(method, url_path, timestamp, body)
        
        return {
            "Wpay-Store-Api-Key": self.api_key,
            "Wpay-Store-Api-Signature": signature,
            "Wpay-Store-Api-Timestamp": timestamp,
            "Content-Type": "application/json"
        }
    
    async def create_order(
        self,
        amount: float,
        currency: str,
        description: str,
        external_id: str,
        timeout_seconds: int = 3600,
        customer_telegram_user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Создание заказа в Wallet Pay
        
        Args:
            amount: Сумма платежа
            currency: Валюта (TON, USDT, BTC)
            description: Описание платежа
            external_id: Внешний ID заказа
            timeout_seconds: Таймаут заказа в секундах
            customer_telegram_user_id: Telegram ID пользователя
            
        Returns:
            Данные созданного заказа
        """
        try:
            url_path = "/order"
            
            order_data = {
                "amount": {
                    "amount": str(amount),
                    "currencyCode": currency
                },
                "description": description,
                "externalId": external_id,
                "timeoutSeconds": timeout_seconds,
                "customerTelegramUserId": customer_telegram_user_id
            }
            
            if self.webhook_url:
                order_data["returnUrl"] = f"{self.webhook_url}/success"
                order_data["failReturnUrl"] = f"{self.webhook_url}/fail"
            
            body = json.dumps(order_data)
            headers = self._get_headers("POST", url_path, body)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}{url_path}",
                    headers=headers,
                    data=body
                ) as response:
                    result = await response.json()
                    
                    if response.status == 200 and result.get("status") == "SUCCESS":
                        logger.info(f"Wallet Pay заказ создан: {external_id}")
                        return {
                            "success": True,
                            "order_id": result["data"]["id"],
                            "pay_link": result["data"]["payLink"],
                            "direct_pay_link": result["data"]["directPayLink"],
                            "order_data": result["data"]
                        }
                    else:
                        logger.error(f"Ошибка создания Wallet Pay заказа: {result}")
                        return {
                            "success": False,
                            "error": result.get("message", "Unknown error"),
                            "code": result.get("code")
                        }
                        
        except Exception as e:
            logger.error(f"Ошибка создания Wallet Pay заказа: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_order_preview(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        Получение превью заказа
        
        Args:
            order_id: ID заказа
            
        Returns:
            Данные превью заказа
        """
        try:
            url_path = f"/order/preview"
            
            params = {"id": order_id}
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            full_path = f"{url_path}?{query_string}"
            
            headers = self._get_headers("GET", full_path)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}{full_path}",
                    headers=headers
                ) as response:
                    result = await response.json()
                    
                    if response.status == 200 and result.get("status") == "SUCCESS":
                        return result["data"]
                    else:
                        logger.error(f"Ошибка получения превью заказа {order_id}: {result}")
                        return None
                        
        except Exception as e:
            logger.error(f"Ошибка получения превью заказа {order_id}: {e}")
            return None
    
    async def get_order_list(
        self,
        offset: int = 0,
        count: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Получение списка заказов
        
        Args:
            offset: Смещение
            count: Количество заказов
            
        Returns:
            Список заказов
        """
        try:
            url_path = "/order/list"
            
            params = {"offset": offset, "count": count}
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            full_path = f"{url_path}?{query_string}"
            
            headers = self._get_headers("GET", full_path)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}{full_path}",
                    headers=headers
                ) as response:
                    result = await response.json()
                    
                    if response.status == 200 and result.get("status") == "SUCCESS":
                        return result["data"]["items"]
                    else:
                        logger.error(f"Ошибка получения списка заказов: {result}")
                        return []
                        
        except Exception as e:
            logger.error(f"Ошибка получения списка заказов: {e}")
            return []
    
    def verify_webhook_signature(self, body: str, signature: str, timestamp: str) -> bool:
        """
        Верификация подписи webhook
        
        Args:
            body: Тело запроса
            signature: Подпись из заголовка
            timestamp: Временная метка
            
        Returns:
            True если подпись верна
        """
        try:
            expected_signature = self._generate_signature("POST", "/webhook", timestamp, body)
            return hmac.compare_digest(signature, expected_signature)
        except Exception as e:
            logger.error(f"Ошибка верификации webhook подписи: {e}")
            return False
    
    async def get_balance(self) -> Optional[Dict[str, Any]]:
        """
        Получение баланса магазина
        
        Returns:
            Данные баланса
        """
        try:
            url_path = "/balance"
            headers = self._get_headers("GET", url_path)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}{url_path}",
                    headers=headers
                ) as response:
                    result = await response.json()
                    
                    if response.status == 200 and result.get("status") == "SUCCESS":
                        return result["data"]
                    else:
                        logger.error(f"Ошибка получения баланса: {result}")
                        return None
                        
        except Exception as e:
            logger.error(f"Ошибка получения баланса: {e}")
            return None
    
    def get_supported_currencies(self) -> List[str]:
        """
        Получение поддерживаемых валют
        
        Returns:
            Список поддерживаемых валют
        """
        return ["TON", "USDT", "BTC", "ETH"]
    
    def is_configured(self) -> bool:
        """
        Проверка конфигурации клиента
        
        Returns:
            True если клиент настроен
        """
        return bool(self.api_key and self.secret_key)

# Глобальный экземпляр Wallet Pay клиента
wallet_pay_client = None

def get_wallet_pay_client() -> WalletPayClient:
    """Получить глобальный экземпляр Wallet Pay клиента"""
    global wallet_pay_client
    if wallet_pay_client is None:
        wallet_pay_client = WalletPayClient()
    return wallet_pay_client
