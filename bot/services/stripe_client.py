# bot/services/stripe_client.py

import os
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import stripe
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class StripeClient:
    """
    Клиент для работы с Stripe API
    Обработка платежей и подписок
    """
    
    def __init__(self):
        self.secret_key = os.getenv("STRIPE_SECRET_KEY")
        self.publishable_key = os.getenv("STRIPE_PUBLISHABLE_KEY")
        self.webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")
        
        if not self.secret_key:
            raise ValueError("STRIPE_SECRET_KEY не установлен")
        
        stripe.api_key = self.secret_key
        logger.info("Stripe клиент инициализирован")
    
    def create_checkout_session(
        self, 
        plan_name: str, 
        amount: int, 
        currency: str, 
        user_id: str,
        success_url: str,
        cancel_url: str,
        metadata: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """
        Создание Stripe Checkout Session
        
        Args:
            plan_name: Название плана
            amount: Сумма в центах
            currency: Валюта (USD, EUR, etc.)
            user_id: ID пользователя
            success_url: URL для успешной оплаты
            cancel_url: URL для отмененной оплаты
            metadata: Дополнительные метаданные
            
        Returns:
            Данные сессии Stripe
        """
        try:
            session_metadata = {
                'user_id': user_id,
                'created_at': datetime.utcnow().isoformat()
            }
            
            if metadata:
                session_metadata.update(metadata)
            
            session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': currency.lower(),
                        'product_data': {
                            'name': f"ChartGenius {plan_name}",
                            'description': f"Subscription to {plan_name}",
                            'images': ['https://your-domain.com/logo.png']  # Опционально
                        },
                        'unit_amount': amount,
                    },
                    'quantity': 1,
                }],
                mode='payment',
                success_url=success_url,
                cancel_url=cancel_url,
                metadata=session_metadata,
                customer_email=None,  # Можно добавить email если есть
                billing_address_collection='auto',
                payment_intent_data={
                    'metadata': session_metadata
                }
            )
            
            logger.info(f"Stripe checkout session создана: {session.id}")
            
            return {
                'success': True,
                'session_id': session.id,
                'url': session.url,
                'payment_intent': session.payment_intent
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe ошибка создания сессии: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Ошибка создания Stripe сессии: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def retrieve_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Получение информации о сессии
        
        Args:
            session_id: ID сессии Stripe
            
        Returns:
            Данные сессии или None
        """
        try:
            session = stripe.checkout.Session.retrieve(session_id)
            
            return {
                'id': session.id,
                'payment_status': session.payment_status,
                'amount_total': session.amount_total,
                'currency': session.currency,
                'customer': session.customer,
                'payment_intent': session.payment_intent,
                'metadata': session.metadata
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe ошибка получения сессии {session_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Ошибка получения Stripe сессии {session_id}: {e}")
            return None
    
    def retrieve_payment_intent(self, payment_intent_id: str) -> Optional[Dict[str, Any]]:
        """
        Получение информации о платеже
        
        Args:
            payment_intent_id: ID платежа
            
        Returns:
            Данные платежа или None
        """
        try:
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            
            return {
                'id': payment_intent.id,
                'status': payment_intent.status,
                'amount': payment_intent.amount,
                'currency': payment_intent.currency,
                'created': payment_intent.created,
                'metadata': payment_intent.metadata
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe ошибка получения платежа {payment_intent_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Ошибка получения Stripe платежа {payment_intent_id}: {e}")
            return None
    
    def handle_webhook(self, payload: str, signature: str) -> Optional[Dict[str, Any]]:
        """
        Обработка Stripe webhook
        
        Args:
            payload: Тело запроса
            signature: Подпись Stripe
            
        Returns:
            Данные события или None
        """
        try:
            if not self.webhook_secret:
                logger.warning("STRIPE_WEBHOOK_SECRET не установлен")
                return None
            
            event = stripe.Webhook.construct_event(
                payload, signature, self.webhook_secret
            )
            
            logger.info(f"Stripe webhook получен: {event['type']}")
            
            return {
                'type': event['type'],
                'data': event['data'],
                'id': event['id'],
                'created': event['created']
            }
            
        except ValueError as e:
            logger.error(f"Неверный payload в Stripe webhook: {e}")
            return None
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Неверная подпись Stripe webhook: {e}")
            return None
        except Exception as e:
            logger.error(f"Ошибка обработки Stripe webhook: {e}")
            return None
    
    def create_customer(self, email: str, name: str = None, metadata: Dict[str, str] = None) -> Optional[str]:
        """
        Создание клиента в Stripe
        
        Args:
            email: Email клиента
            name: Имя клиента
            metadata: Дополнительные метаданные
            
        Returns:
            ID клиента или None
        """
        try:
            customer_data = {'email': email}
            
            if name:
                customer_data['name'] = name
            
            if metadata:
                customer_data['metadata'] = metadata
            
            customer = stripe.Customer.create(**customer_data)
            
            logger.info(f"Stripe клиент создан: {customer.id}")
            return customer.id
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe ошибка создания клиента: {e}")
            return None
        except Exception as e:
            logger.error(f"Ошибка создания Stripe клиента: {e}")
            return None
    
    def get_payment_methods(self, customer_id: str) -> List[Dict[str, Any]]:
        """
        Получение методов оплаты клиента
        
        Args:
            customer_id: ID клиента
            
        Returns:
            Список методов оплаты
        """
        try:
            payment_methods = stripe.PaymentMethod.list(
                customer=customer_id,
                type="card"
            )
            
            return [
                {
                    'id': pm.id,
                    'type': pm.type,
                    'card': {
                        'brand': pm.card.brand,
                        'last4': pm.card.last4,
                        'exp_month': pm.card.exp_month,
                        'exp_year': pm.card.exp_year
                    } if pm.card else None
                }
                for pm in payment_methods.data
            ]
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe ошибка получения методов оплаты: {e}")
            return []
        except Exception as e:
            logger.error(f"Ошибка получения методов оплаты: {e}")
            return []
    
    def refund_payment(self, payment_intent_id: str, amount: int = None, reason: str = None) -> Dict[str, Any]:
        """
        Возврат платежа
        
        Args:
            payment_intent_id: ID платежа
            amount: Сумма возврата (если не указана - полный возврат)
            reason: Причина возврата
            
        Returns:
            Результат возврата
        """
        try:
            refund_data = {'payment_intent': payment_intent_id}
            
            if amount:
                refund_data['amount'] = amount
            
            if reason:
                refund_data['reason'] = reason
            
            refund = stripe.Refund.create(**refund_data)
            
            logger.info(f"Stripe возврат создан: {refund.id}")
            
            return {
                'success': True,
                'refund_id': refund.id,
                'status': refund.status,
                'amount': refund.amount
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe ошибка возврата: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Ошибка Stripe возврата: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_balance(self) -> Optional[Dict[str, Any]]:
        """
        Получение баланса Stripe аккаунта
        
        Returns:
            Данные баланса или None
        """
        try:
            balance = stripe.Balance.retrieve()
            
            return {
                'available': balance.available,
                'pending': balance.pending,
                'livemode': balance.livemode
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe ошибка получения баланса: {e}")
            return None
        except Exception as e:
            logger.error(f"Ошибка получения Stripe баланса: {e}")
            return None

# Глобальный экземпляр Stripe клиента
stripe_client = None

def get_stripe_client() -> StripeClient:
    """Получить глобальный экземпляр Stripe клиента"""
    global stripe_client
    if stripe_client is None:
        stripe_client = StripeClient()
    return stripe_client
