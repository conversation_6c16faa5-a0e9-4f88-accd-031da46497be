# bot/services/telegram_payments.py

import logging
from typing import Dict, Any, Optional
from datetime import datetime
import json

from telegram import Update, LabeledPrice, PreCheckoutQuery
from telegram.ext import ContextTypes

from backend.services.oracle_client import oracle_client
from backend.config.config import logger

class TelegramPaymentsHandler:
    """
    Обработчик Telegram Payments (Stars)
    """
    
    def __init__(self, payment_token: str):
        self.payment_token = payment_token
        logger.info("Telegram Payments handler инициализирован")
    
    async def create_invoice(
        self,
        context: ContextTypes.DEFAULT_TYPE,
        chat_id: int,
        title: str,
        description: str,
        payload: str,
        amount: int,
        currency: str = 'XTR',  # Telegram Stars
        photo_url: str = None
    ) -> bool:
        """
        Создание инвойса для Telegram Payments
        
        Args:
            context: Telegram context
            chat_id: ID чата
            title: Заголовок инвойса
            description: Описание
            payload: Полезная нагрузка
            amount: Сумма в минимальных единицах
            currency: Валюта (XTR для Stars)
            photo_url: URL изображения
            
        Returns:
            True если инвойс отправлен успешно
        """
        try:
            prices = [LabeledPrice(label=title, amount=amount)]
            
            invoice_params = {
                'chat_id': chat_id,
                'title': title,
                'description': description,
                'payload': payload,
                'provider_token': self.payment_token,
                'currency': currency,
                'prices': prices,
                'start_parameter': f'invoice_{chat_id}_{int(datetime.utcnow().timestamp())}'
            }
            
            if photo_url:
                invoice_params['photo_url'] = photo_url
            
            # Дополнительные параметры для Telegram Stars
            if currency == 'XTR':
                invoice_params.update({
                    'max_tip_amount': 0,
                    'suggested_tip_amounts': [],
                    'need_name': False,
                    'need_phone_number': False,
                    'need_email': False,
                    'need_shipping_address': False,
                    'send_phone_number_to_provider': False,
                    'send_email_to_provider': False,
                    'is_flexible': False
                })
            
            message = await context.bot.send_invoice(**invoice_params)
            
            # Сохраняем информацию об инвойсе
            invoice_data = {
                'chat_id': chat_id,
                'message_id': message.message_id,
                'title': title,
                'description': description,
                'payload': payload,
                'amount': amount,
                'currency': currency,
                'status': 'sent',
                'created_at': datetime.utcnow().isoformat()
            }
            
            oracle_client.insert_document('telegram_invoices', payload, invoice_data)
            
            logger.info(f"Telegram инвойс отправлен: {payload}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки Telegram инвойса: {e}")
            return False
    
    async def handle_pre_checkout_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Обработка pre-checkout запроса
        
        Args:
            update: Telegram update
            context: Telegram context
        """
        try:
            query = update.pre_checkout_query
            
            # Проверяем payload
            payload = query.invoice_payload
            
            # Получаем информацию об инвойсе
            invoice_data = oracle_client.get_document('telegram_invoices', payload)
            
            if not invoice_data:
                await query.answer(ok=False, error_message="Invoice not found")
                logger.warning(f"Инвойс не найден: {payload}")
                return
            
            # Проверяем сумму
            if query.total_amount != invoice_data.get('amount'):
                await query.answer(ok=False, error_message="Amount mismatch")
                logger.warning(f"Несоответствие суммы в инвойсе {payload}")
                return
            
            # Дополнительные проверки можно добавить здесь
            # Например, проверка доступности товара, лимитов пользователя и т.д.
            
            # Подтверждаем платеж
            await query.answer(ok=True)
            
            # Обновляем статус инвойса
            invoice_data['status'] = 'pre_checkout_confirmed'
            invoice_data['pre_checkout_at'] = datetime.utcnow().isoformat()
            oracle_client.update_document('telegram_invoices', payload, invoice_data)
            
            logger.info(f"Pre-checkout подтвержден: {payload}")
            
        except Exception as e:
            logger.error(f"Ошибка обработки pre-checkout: {e}")
            await query.answer(ok=False, error_message="Internal error")
    
    async def handle_successful_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Обработка успешного платежа
        
        Args:
            update: Telegram update
            context: Telegram context
        """
        try:
            payment = update.message.successful_payment
            payload = payment.invoice_payload
            
            # Получаем информацию об инвойсе
            invoice_data = oracle_client.get_document('telegram_invoices', payload)
            
            if not invoice_data:
                logger.error(f"Инвойс не найден для успешного платежа: {payload}")
                return
            
            # Обновляем статус инвойса
            invoice_data.update({
                'status': 'completed',
                'telegram_payment_charge_id': payment.telegram_payment_charge_id,
                'provider_payment_charge_id': payment.provider_payment_charge_id,
                'total_amount': payment.total_amount,
                'currency': payment.currency,
                'completed_at': datetime.utcnow().isoformat()
            })
            
            oracle_client.update_document('telegram_invoices', payload, invoice_data)
            
            # Обрабатываем конкретный тип платежа
            await self._process_payment_by_type(update, context, payload, payment)
            
            logger.info(f"Telegram платеж завершен: {payload}")
            
        except Exception as e:
            logger.error(f"Ошибка обработки успешного платежа: {e}")
    
    async def _process_payment_by_type(self, update: Update, context: ContextTypes.DEFAULT_TYPE, payload: str, payment) -> None:
        """
        Обработка платежа в зависимости от типа
        
        Args:
            update: Telegram update
            context: Telegram context
            payload: Полезная нагрузка
            payment: Данные платежа
        """
        try:
            user_id = str(update.effective_user.id)
            
            # Парсим payload для определения типа платежа
            if payload.startswith('subscription_'):
                await self._process_subscription_payment(user_id, payload, payment)
            elif payload.startswith('credits_'):
                await self._process_credits_payment(user_id, payload, payment)
            elif payload.startswith('feature_'):
                await self._process_feature_payment(user_id, payload, payment)
            else:
                logger.warning(f"Неизвестный тип платежа: {payload}")
            
            # Отправляем подтверждение пользователю
            await update.message.reply_text(
                f"✅ Payment successful!\n"
                f"Amount: {payment.total_amount} {payment.currency}\n"
                f"Transaction ID: {payment.telegram_payment_charge_id}\n\n"
                f"Thank you for your purchase!"
            )
            
        except Exception as e:
            logger.error(f"Ошибка обработки платежа по типу: {e}")
    
    async def _process_subscription_payment(self, user_id: str, payload: str, payment) -> None:
        """Обработка платежа за подписку"""
        try:
            # Извлекаем plan_id из payload
            parts = payload.split('_')
            if len(parts) >= 3:
                plan_id = parts[1]
                
                # Импортируем обработчик платежей
                from bot.handlers.payments import get_payment_handler
                payment_handler = get_payment_handler("", "")  # Dummy keys for this method
                
                # Активируем подписку
                success = await payment_handler.handle_successful_payment(
                    user_id, plan_id, 'telegram_stars'
                )
                
                if success:
                    logger.info(f"Подписка {plan_id} активирована для пользователя {user_id}")
                else:
                    logger.error(f"Ошибка активации подписки {plan_id} для пользователя {user_id}")
            
        except Exception as e:
            logger.error(f"Ошибка обработки платежа за подписку: {e}")
    
    async def _process_credits_payment(self, user_id: str, payload: str, payment) -> None:
        """Обработка платежа за кредиты"""
        try:
            # Извлекаем количество кредитов из payload
            parts = payload.split('_')
            if len(parts) >= 2:
                credits_amount = int(parts[1])
                
                # Получаем данные пользователя
                user_data = oracle_client.get_document('users', user_id) or {}
                current_credits = user_data.get('credits', 0)
                
                # Добавляем кредиты
                user_data['credits'] = current_credits + credits_amount
                user_data['credits_updated_at'] = datetime.utcnow().isoformat()
                
                # Сохраняем
                oracle_client.update_document('users', user_id, user_data)
                
                logger.info(f"Добавлено {credits_amount} кредитов пользователю {user_id}")
            
        except Exception as e:
            logger.error(f"Ошибка обработки платежа за кредиты: {e}")
    
    async def _process_feature_payment(self, user_id: str, payload: str, payment) -> None:
        """Обработка платежа за отдельную функцию"""
        try:
            # Извлекаем название функции из payload
            parts = payload.split('_')
            if len(parts) >= 2:
                feature_name = parts[1]
                
                # Получаем данные пользователя
                user_data = oracle_client.get_document('users', user_id) or {}
                purchased_features = user_data.get('purchased_features', [])
                
                # Добавляем функцию
                if feature_name not in purchased_features:
                    purchased_features.append(feature_name)
                    user_data['purchased_features'] = purchased_features
                    user_data['features_updated_at'] = datetime.utcnow().isoformat()
                    
                    # Сохраняем
                    oracle_client.update_document('users', user_id, user_data)
                    
                    logger.info(f"Функция {feature_name} активирована для пользователя {user_id}")
            
        except Exception as e:
            logger.error(f"Ошибка обработки платежа за функцию: {e}")
    
    def get_payment_history(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Получение истории платежей пользователя
        
        Args:
            user_id: ID пользователя
            limit: Лимит записей
            
        Returns:
            Список платежей
        """
        try:
            # Получаем все инвойсы пользователя
            all_invoices = oracle_client.query_documents('telegram_invoices', limit=1000)
            
            # Фильтруем по пользователю и статусу
            user_payments = []
            for invoice in all_invoices:
                payload = invoice.get('payload', '')
                if payload.endswith(f'_{user_id}') and invoice.get('status') == 'completed':
                    user_payments.append({
                        'payload': payload,
                        'amount': invoice.get('total_amount'),
                        'currency': invoice.get('currency'),
                        'completed_at': invoice.get('completed_at'),
                        'charge_id': invoice.get('telegram_payment_charge_id')
                    })
            
            # Сортируем по дате (новые первые) и ограничиваем
            user_payments.sort(key=lambda x: x.get('completed_at', ''), reverse=True)
            return user_payments[:limit]
            
        except Exception as e:
            logger.error(f"Ошибка получения истории платежей: {e}")
            return []

# Глобальный экземпляр обработчика Telegram Payments
telegram_payments_handler = None

def get_telegram_payments_handler(payment_token: str) -> TelegramPaymentsHandler:
    """Получить глобальный экземпляр обработчика Telegram Payments"""
    global telegram_payments_handler
    if telegram_payments_handler is None:
        telegram_payments_handler = TelegramPaymentsHandler(payment_token)
    return telegram_payments_handler
